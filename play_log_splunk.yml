- name:           Log and Splunk
  #hosts:          proxy4ext
  hosts: proxy
  gather_facts:   true
  environment:
    # http_proxy: "{{ proxy_host }}"
    # https_proxy: "{{ proxy_host }}"
    no_proxy: "*"
    
  tasks:
    # - name: Test for running splunkd
    #   shell: ps axuf|grep 'splunkd'|grep -v "grep" | tr -d "\n" | cat
    #   register: test_running_splunkd
    #   changed_when: False
      
    # - name: print
    #   debug: 
    #     msg: "{{ test_running_splunkd }}"

    # - name: Play install Splunk Fowarde
    #   include_role:
    #     name: install-splunk-forwarder
    #   # when: test_running_splunkd.stdout == ""
    #   vars:
    #     SPLUNK_INSTALL_PACKAGE: "http://***********/sources/splunkforwarder-8.1.3.rpm"
    #     COUNTRY_CODE: "VN"
    #     SPLUNK_DEPLOYMENT_SERVER: "dep01-pdcvn1spl.vn.prod:8089"
    #     INDEX_NAME: "SYSOPS"
    #     SOURCE_TYPE: "squid:log"
    #     PATH_MONITOR: "/DATA/squid-logs/access.log"
    #     #PATH for proxy4ext
    #     #PATH_MONITOR: "/var/log/squid/access.log"

    - name: Install Splunk Forwarder
      include_role:
        name: install-splunk-forwarder
      vars: 
        COUNTRY_CODE: "VN"
        #SPLUNK_DEPLOYMENT_SERVER: "{{ global_splunk_depser|default('dep01-prod-spl.vn.prod:8089') }}"
        SPLUNK_DEPLOYMENT_SERVER: "dep01-prod-spl.vn.prod:8089"
        INDEX_NAME: "hcg_vn_sysops_prod"
        SOURCE_TYPE: "squid:log"
        PATH_MONITOR: "/DATA/squid-logs/access.log" # https://phoenixnap.com/kb/how-to-view-read-linux-log-files
#      tags:
#      - InstallSplunkFwd
#      when: global_register_install_splunkfwd | bool and global_register_install_splunkfwd is defined

    - name: config-logrotate
      include_role:
        name: ansible-logrotate

    # - name: copy repo logstash
    #   copy:
    #    src: "{{ item }}"
    #    dest: "/etc/yum.repos.d/"
    #   with_fileglob:
    #    - "./files/repo/*.repo"


    # - name: Install Logstash.
    #   package:
    #     name: logstash
    #     state: present
    
    # - name: logstash-output-syslog
    #   shell: /usr/share/logstash/bin/logstash-plugin install logstash-output-syslog 

    # - name: copy logstash config qradar 
    #   copy:
    #    src: "{{ item.src }}"
    #    dest: "{{ item.dest }}"
    #    force: yes
    #   with_items:
    #    - { src: "conf/logstashsquid.conf", dest: "/etc/logstash/conf.d/" }
    #    - { src: "conf/logstash.service", dest: "/etc/systemd/system/" }

    # - name: restart logstash
    #   service: name=logstash state=restarted


    
  

