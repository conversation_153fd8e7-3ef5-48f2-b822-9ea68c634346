- name: Prepare ansible environment
  hosts: localhost
  gather_facts: no

  vars:
    # global vars from ENV
    global_gitlab_token: "{{ lookup('env', 'GITLAB_TOKEN') }}"
    global_run_env: "{{ lookup('env', 'RUN_ENV') | default('prod', true) }}"
    
    # playbook vars
    group_vars_path: country/vn/devops/ansible/group_vars
    group_vars_files:
      - all.yml

    baseline_repo_path: "country/vn/devops/playbooks/oracle_linux_vm_baseline"
    baseline_repo_rev: "master"
    baseline_files_list:
      - "{{ playbook_dir }}/{{ baseline_repo_path | basename }}/baseline_requirements.tmpl"
      - "{{ playbook_dir }}/{{ baseline_repo_path | basename }}/play_ol7_baseline.yml"
      - "{{ playbook_dir }}/{{ baseline_repo_path | basename }}/baseline-job-ci.yml"
    
    secrets_var_path: "country/vn/devops/secret/{{ global_run_env }}"
    secrets_var_files: 
      - secrets.yml
    secrets_files:
      - ansible_vault_password
      - id_rsa

  # pre_tasks:
  # - name:                                    Cleanup tmp and prepare fresh one
  #   shell:                                   rm -rf tmp && mkdir tmp

  tasks:
    - name: "Fetch global ansible variabels"
      include_role:
        name: gitlab_fetch_files
      vars:
        giltab_fetch_files_gitlab_private_token: "{{ global_gitlab_token }}"
        files:
          - path: "{{ item }}"
            dest: group_vars
            project_path: "{{ group_vars_path }}"
      with_items: "{{ group_vars_files }}"
      when: group_vars_files is defined and group_vars_files | length > 0

    - name: "Fetch secrets var yaml file"
      include_role:
        name: gitlab_fetch_files
      vars:
        giltab_fetch_files_gitlab_private_token: "{{ global_gitlab_token }}"
        files:
          - path: "{{ item }}"
            dest: .
            project_path: "{{ secrets_var_path }}"
      with_items: "{{ secrets_var_files }}"
      when: secrets_var_files is defined and secrets_var_files | length > 0

    - name: "Fetch other secret files"
      include_role:
        name: gitlab_fetch_files
      vars:
        giltab_fetch_files_gitlab_private_token: "{{ global_gitlab_token }}"
        files:
          - path: "{{ item }}"
            dest: .
            project_path: "{{ secrets_var_path }}"
      with_items: "{{ secrets_files }}"
      when: secrets_files is defined and secrets_files | length > 0

    # - name: "Clone baseline playbook from git"
    #   include_role:
    #     name: utils_git_clone
    #   vars:
    #     repository_uri: "https://oauth2:{{ global_gitlab_token }}@git.homecredit.net/{{ baseline_repo_path }}"
    #     repository_folder: "{{ playbook_dir }}/{{ baseline_repo_path | basename }}"
    #     repository_branch: "{{ baseline_repo_rev | default('master') }}"
    #   when: baseline_repo_path is defined and baseline_repo_path != ""
    #   tags:
    #     - fetch_baseline_repo

    # - name: "Copy baseline scripts to playbook"
    #   copy:
    #     src: "{{ item }}"
    #     dest: "{{ playbook_dir }}/"
    #   with_fileglob: "{{ baseline_files_list }}"
    #   tags:
    #     - copy_baseline_files_to_playbook_dir
    #   when: baseline_repo_path is defined and baseline_repo_path != ""

    - name: fix file permission
      file:
        path: "{{ item }}"
        mode: "0600"
      with_items: "{{ secrets_files }}"
      when: secrets_files is defined and secrets_files | length > 0
      tags:
        - fix_files_permission

    # - name: Load secrets files
    #   include_vars: "{{ item }}"
    #   with_items: "{{ secrets_var_files }}"
    #   when: secrets_var_files is defined and secrets_var_files | length > 0
    #   tags:
    #     - load_secrets_vars