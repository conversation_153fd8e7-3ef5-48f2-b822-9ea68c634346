# Install a role from Git
- name:    gitlab_fetch_files
  src:     https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/ansible/roles/gitlab_fetch_files.git
  version: origin/master
  scm:     git

- name: utils_git_clone
  src: https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/ansible/roles/utils_git_clone.git
  scm: git
  version: v0.3

- name:    hcvn-dynamic-dns
  src:     https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/ansible/roles/dnsapi.git
  version: origin/master
  scm:     git

- name:    ntp_client
  src:     https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/country/vn/devops/ansible/roles/ntp_client.git
  version: v0.2
  scm:     git

- name:    deploy-qradar-syslog
  src:     https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/country/vn/devops/ansible/roles/deploy-qradar-syslog.git
  version: origin/master
  scm:     git

- name:    logger
  src:     https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/ansible/roles/logger.git
  version: origin/master
  scm:     git

- name:    dynamic_motd
  src:     https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/ansible/roles/dynamic_motd.git
  version: origin/master
  scm:     git

- name:    linux_baseline_config
  src:     https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/country/vn/devops/ansible/roles/linux_baseline_config.git
  version: v_0.1
  scm:     git

- name:    nrpe-agent-deploy
  src:     https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/country/vn/devops/ansible/roles/nrpe-agent-deploy.git
  version: v0.4
  scm:     git

- name:    oracle_linux_repo
  src:     https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/country/vn/devops/ansible/roles/oracle_linux_repo.git
  version: khandt
  scm:     git

- name:    dns_config
  src:     https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/country/vn/devops/ansible/roles/dns_config.git
  version: v0.2
  scm:     git

- name:    security_eit
  src:     https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/country/vn/devops/ansible/roles/security_eit.git
  version: v_0.2
  scm:     git  

#- name: install_sentinelone
- name: install_sentinelone_agent
  src: https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/country/vn/devops/ansible/roles/install_sentinelone_agent.git
#  src: https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/country/vn/security/ansible/roles/install_sentinelone.git
  scm: git
  version: origin/master

- name:    install-splunk-forwarder
  src:     https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/country/vn/devops/ansible/roles/install-splunk-forwarder.git
#  version: origin/master
  vesion: v0.3
  scm:     git

- name: api-register-ipaddr
  src: https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/country/vn/devops/ansible/roles/api-register-ipaddr.git
  version: v0.6
# version: origin/master
  scm:     git

- name:     api_register_to_cba
  src:      https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/country/vn/devops/ansible/roles/api_register_to_cba.git
  version: v0.1
  scm:      git  

- name: node_exporter
  src: https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/country/vn/devops/ansible/roles/node_exporter.git
  version: origin/master
  scm: git



# Install a role from Git
- name:    gitlab_fetch_files
  src:     https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/ansible/roles/gitlab_fetch_files.git
  version: origin/master
  scm:     git

- name: utils_git_clone
  src: https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/ansible/roles/utils_git_clone.git
  scm: git
  version: v0.3

- name:    hcvn-dynamic-dns
  src:     https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/ansible/roles/dnsapi.git
  version: origin/master
  scm:     git

- name:     vmware_guest_custom_attributes
  src:      https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/country/vn/devops/ansible/roles/vmware_guest_custom_attributes.git
  version: origin/master
  scm:      git

- name: install-repo-foreman
  src: https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/country/vn/devops/ansible/roles/install-repo-foreman.git
  version: origin/master
  scm: git

- name: netbackup
  src: https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/country/vn/devops/ansible/roles/netbackup.git
  version: origin/master
  scm: git

- name: install-splunk-forwarder
  src: https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/country/vn/devops/ansible/roles/install-splunk-forwarder.git
  version: origin/master
  scm: git

- name:     api_register_to_cba
  src:      https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/country/vn/devops/ansible/roles/api_register_to_cba.git
  version: origin/master
  scm:      git


- name:     vsphere_vm_tags
  src:      https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/country/vn/devops/deploy/vsphere_vm_tags.git
  version: origin/master
  scm:      git
  
- name:     ansible-logrotate
  src:      https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/country/vn/devops/ansible/roles/ansible-logrotate.git
  version: origin/master
  scm:      git  

- name:    lvm-resize
  src:     https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/country/vn/devops/ansible/roles/lvm-resize.git
  version: origin/master
  scm:     git

- name:     node_exporter
  src:      https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/country/vn/devops/ansible/roles/node_exporter.git
  version: origin/master
  scm:      git 
  
- name:    create_qualys_account
  src:     https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/country/vn/devops/ansible/roles/create_qualys_account.git
  version: origin/master
  scm:     git

- name:    repmgr
  src:     https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/country/vn/devops/ansible/roles/repmgr.git  
  version: origin/master
  scm:     git

- name:    deploy-qradar-syslog
  src:     https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/country/vn/devops/ansible/roles/deploy-qradar-syslog
  version: origin/master
  scm:     git


- name:    ansible-promtail
  src:     https://gitlab-ci-token:${CI_JOB_TOKEN}@git.homecredit.net/country/vn/devops/ansible/roles/ansible-promtail
  version: origin/master
  scm:     git