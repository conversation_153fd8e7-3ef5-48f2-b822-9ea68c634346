---
- name: HA proxy setup
  hosts: lb
  vars:
    vip: "***********"
    package:
      - keepalived
      - haproxy
    sysctl:
      - name: fs.file-max
        value: 2097152
      - name: kernel.core_uses_pid
        value: 1
      - name: kernel.msgmax
        value: 65533
      - name: kernel.msgmnb
        value: 65534
      - name: kernel.pid_max
        value: 65535
      - name: kernel.shmall
        value: 268435456
      - name: kernel.shmmax
        value: 268435456
      - name: net.core.netdev_max_backlog
        value: 4096
      - name: net.core.optmem_max
        value: 65535
      - name: net.core.rmem_default
        value: 262144
      - name: net.core.rmem_max
        value: 16777216
      - name: net.core.somaxconn
        value: 32768
      - name: net.core.wmem_default
        value: 262144
      - name: net.core.wmem_max
        value: 16777216
      - name: net.ipv4.conf.all.accept_redirects
        value: 0
      - name: net.ipv4.ip_local_port_range
        value: 10000 65535
      - name: net.ipv4.tcp_fin_timeout
        value: 15
      - name: net.ipv4.tcp_keepalive_intvl
        value: 15
      - name: net.ipv4.tcp_keepalive_probes
        value: 5
      - name: net.ipv4.tcp_keepalive_time
        value: 300
      - name: net.ipv4.tcp_max_syn_backlog
        value: 4096
      - name: net.ipv4.tcp_max_tw_buckets
        value: 1440000
      - name: net.ipv4.tcp_no_metrics_save
        value: 1
      - name: net.ipv4.tcp_rfc1337
        value: 1
      - name: net.ipv4.tcp_rmem
        value: 8192 87380 16777216
      - name: net.ipv4.tcp_slow_start_after_idle
        value: 0
      - name: net.ipv4.tcp_syn_retries
        value: 2
      - name: net.ipv4.tcp_synack_retries
        value: 2
      - name: net.ipv4.tcp_tw_reuse
        value: 0
      - name: net.ipv4.tcp_wmem
        value: 8192 65536 16777216
      - name: net.ipv4.udp_rmem_min
        value: 16384
      - name: net.ipv4.udp_wmem_min
        value: 16384
      - name: net.unix.max_dgram_qlen
        value: 50
      - name: vm.dirty_background_ratio
        value: 5
      - name: vm.dirty_ratio
        value: 30
      - name: vm.min_free_kbytes
        value: 65535
      - name: vm.mmap_min_addr
        value: 4096
      - name: vm.swappiness
        value: 30
      - name: net.ipv4.ip_nonlocal_bind
        value: 1
      - name: net.ipv4.ip_forward
        value: 1
  tasks:
    - name: reset sysctl.conf
      shell: echo -n "" > /etc/sysctl.conf
  # edit sysctl.conf
    - name: Config sysctl.conf
      sysctl:
        name: "{{ item.name }}"
        value: "{{ item.value }}"
        state: present
        sysctl_set: true
        reload: yes
      with_items: 
        - "{{ sysctl }}"     
    # Install squid
    - name: Install keepalived and haproxy
      dnf:
        name: "{{ package }}"
        state: present

    #copy check_script.sh
    - name: check_script.sh
      copy:
        src: "check_script.sh"
        dest: "/etc/keepalived/check_script.sh"
        mode: 0755
        owner: root
        group: root 

    - name: copy to master keepalived.conf
      copy:
        src: "keepalived-master.conf"
        dest: "/etc/keepalived/keepalived.conf"
        mode: 0644
        owner: root
        group: root 
      when: (inventory_hostname in groups['lb']) and (inventory_hostname == "lbproxyprod01.hcnet.vn")

    - name: copy to backup keepalived.conf
      copy:
        src: "keepalived-backup.conf"
        dest: "/etc/keepalived/keepalived.conf"
        mode: 0644
        owner: root
        group: root
      when: (inventory_hostname in groups['lb']) and (inventory_hostname == "lbproxyprod02.hcnet.vn")

    - name: haproxy
      template:
        src: "haproxy.cfg.j2"
        dest: "/etc/haproxy/haproxy.cfg"
        mode: 0644
        force: yes 

    # Enable keepalived and haproxy
    - name: Enable service keepalived, and not touch the running state
      service:
        name: keepalived
        enabled: yes
    - name: Enable service haproxy, and not touch the running state
      service:
        name: haproxy
        enabled: yes
    - name: restart haproxy
      service: name=haproxy state=restarted        
    - name: restart keepalived
      service: name=keepalived state=restarted