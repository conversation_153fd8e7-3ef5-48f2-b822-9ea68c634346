- name:           DNS host setup
  hosts:          all
  gather_facts:   true
  # environment:
  #   http_proxy: "{{ proxy_host }}"
  #   https_proxy: "{{ proxy_host }}"
  connection:     local
  vars:
    dns_api_url: https://run-tools.homecredit.vn/dnsphpadmin/api.php
    dns_api_token: !vault |
          $ANSIBLE_VAULT;1.2;AES256;nonprod
          34656439633232306239613439343534336466393536363066663464303539663635666532623534
          3465653165623535333163636336653465646230353530620a333366636433656437373532353565
          37396663643935613634313834333866313866616362626133393033623539623666646565646465
          3930613336393831630a363461623636306334633966643565366138663234366164626537663765
          38356530353865343730383138616631386334333264323339616565613839303161376637623730
          3538383337313934336335386336336264366338343961383966
    dns_zone: hcnet.vn  
    url:          "{{ dns_api_url }}"
    token:        "{{ dns_api_token }}"
  # vars_files:
  #   - group_vars/all.yml
  tasks:
  - name:         "DNS API cluster host task"
    include_role:
      name:       hcvn-dynamic-dns
    vars:
      
      ttl:        600
      type:       A
      zone:       "{{ dns_zone }}" #hcnet.vn
      # zone:       "{{ dns_zone_hcvn }}" #homecredit.vn
      value:      "{{ ansible_host }}"
      dns:        "{{ inventory_hostname }}"         
