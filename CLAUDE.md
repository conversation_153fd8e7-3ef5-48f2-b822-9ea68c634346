# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an Ansible-based infrastructure deployment project for a proxy server infrastructure with high availability. The project provisions and configures:

- Squid proxy servers across two data centers (PDC and DRC)
- HAProxy load balancers with keepalived for high availability
- Supporting services including DNS, logging, monitoring, and security components

## Common Commands

### Prerequisites

```bash
# Set required environment variables
export GITLAB_TOKEN="your_gitlab_token"
export RUN_ENV="prod"  # defaults to 'prod' if not set
```

### Infrastructure Deployment

```bash
# Prepare the ansible environment (fetches credentials and configurations)
ansible-playbook prepare.yml

# Deploy to specific host groups
ansible-playbook play_squid.yml -e target_hosts=squid-pdc    # PDC squid servers
ansible-playbook play_squid.yml -e target_hosts=squid-drc    # DRC squid servers (default)
ansible-playbook play_squid.yml -e target_hosts=proxy        # All proxy servers

# Deploy HAProxy load balancers
ansible-playbook play_haproxy.yml

# Deploy keepalived for high availability
ansible-playbook play_keepalived_drc.yml

# Deploy OS baseline configuration
ansible-playbook play_os_baseline.yml

# Deploy monitoring with Promtail
ansible-playbook play_promtail.yml

# Deploy node exporter for monitoring
ansible-playbook play_node_exporter.yml

# Deploy DNS configuration
ansible-playbook play_dns.yml

# Deploy QRadar syslog configuration
ansible-playbook play_deploy_qradar_syslog.yml

# Deploy log forwarding to Splunk
ansible-playbook play_log_splunk.yml

# Deploy Qualys agent
ansible-playbook play_qualys.yml
```

### Terraform Infrastructure

```bash
# Navigate to terraform directory
cd terraform/

# Initialize Terraform
terraform init

# Plan infrastructure changes
terraform plan

# Apply infrastructure changes
terraform apply

# Destroy infrastructure
terraform destroy
```

### Ansible Inventory Management

```bash
# Test connectivity to all hosts
ansible all -i inventory.yml -m ping

# Test connectivity to specific groups
ansible proxy -i inventory.yml -m ping        # All proxy servers
ansible squid-pdc -i inventory.yml -m ping    # PDC squid servers
ansible squid-drc -i inventory.yml -m ping    # DRC squid servers
ansible allserver -i inventory.yml -m ping    # All servers including load balancers

# Run ad-hoc commands
ansible all -i inventory.yml -a "uptime"
ansible proxy -i inventory.yml -a "systemctl status squid"
ansible allserver -i inventory.yml -a "df -h"

# Check specific services
ansible proxy -i inventory.yml -m service -a "name=squid state=started"
```

## Architecture Overview

### Infrastructure Components

1. **Proxy Servers**: 
   - PDC (Primary Data Center): proxy-server01-03.hcnet.vn (***********-29)
   - DRC (Disaster Recovery Center): proxy-serverdrc01-02.hcnet.vn (***********-23)

2. **Load Balancers**: 
   - lbproxyprod01.hcnet.vn (***********) - Master
   - lbproxyprod02.hcnet.vn (***********) - Backup

3. **High Availability**:
   - Virtual IP (VIP): ***********
   - Keepalived for failover
   - HAProxy for load balancing

### Configuration Structure

- **inventory.yml**: Defines all server groups and connection details
- **group_vars/**: Contains environment-specific variables
  - `all.yml`: Global variables and settings
  - `squid-pdc.yml` / `squid-drc.yml`: Data center specific configurations
- **files/**: Configuration files and ACL lists
  - Squid configuration files (`squid-pdc.conf`, `squid-drc.conf`)
  - URL filtering lists (blacklist, whitelist, blocked domains)
  - Keepalived configurations
- **templates/**: Jinja2 templates for dynamic configuration
  - `haproxy.cfg.j2`: HAProxy configuration template

### Data Center Setup

The infrastructure spans two data centers:
- **PDC (Primary)**: **********/24 network
- **DRC (Disaster Recovery)**: **********/24 network

### Security and Filtering

Squid proxy includes multiple filtering mechanisms:
- `ads_blocked_urls`: Advertisement blocking
- `atp_block_domains`: ATP (Advanced Threat Protection) domain blocking
- `atp_block_ip`: ATP IP blocking
- `blacklist_urls`: General URL blacklist
- `whitelist_urls`: Allowed URLs
- `block_src`: Source IP blocking
- `blocked_networks`: Network/IP blocking (VLANs, subnets, or individual IPs)
- `allowed_networks`: Network/IP exceptions (allow list overrides)

### Terraform Module Structure

Uses vSphere provider for VM provisioning with:
- Dual provider setup for PDC and DRC
- Modular VM creation (currently vm07 active in DRC)
- Standardized VM tagging for ownership and environment tracking
- Oracle Linux 8/9 templates

## Environment Variables

Key environment variables used:
- `GITLAB_TOKEN`: For accessing GitLab repositories
- `RUN_ENV`: Environment (defaults to 'prod')

## Deployment Workflow

The typical deployment sequence follows this pattern:

1. **Environment Preparation**: Run `prepare.yml` to fetch credentials and configurations
2. **Infrastructure**: Use Terraform to provision VMs if needed
3. **OS Baseline**: Deploy base OS configuration with `play_os_baseline.yml`
4. **Core Services**: Deploy Squid proxies and HAProxy load balancers
5. **High Availability**: Configure keepalived for failover
6. **Monitoring & Logging**: Deploy Promtail, node exporter, and log forwarding
7. **Security**: Deploy Qualys agent and configure QRadar logging

## Ansible Vault and Secrets

```bash
# View encrypted secrets (requires vault password)
ansible-vault view secrets.yml

# Edit encrypted secrets
ansible-vault edit secrets.yml

# Encrypt new files
ansible-vault encrypt new_secret_file.yml
```

## Network Blocking Management

The proxy uses a flexible system for blocking networks/IPs while allowing specific exceptions:

### Files:
- **`blocked_networks`**: Contains networks/IPs to block (subnets, VLANs, or individual IPs)
- **`allowed_networks`**: Contains networks/IPs that should be allowed despite other blocks

### Usage Examples:
```bash
# Block entire VLANs/subnets
echo "***********/24" >> files/blocked_networks
echo "***********/24" >> files/blocked_networks

# Block individual IPs
echo "*************" >> files/blocked_networks
echo "*************" >> files/blocked_networks

# Allow exceptions (can be IPs or subnets)
echo "************" >> files/allowed_networks        # Single IP exception
echo "*************/30" >> files/allowed_networks    # Small subnet exception

# After changes, redeploy squid configuration
ansible-playbook play_squid.yml -e target_hosts=squid-pdc
```

### Rule Order (important):
1. `allowed_networks` (allow list) are checked first
2. `blocked_networks` (block list) are denied second
3. Other access rules follow

## Important Notes

- All playbooks use `ansible_ssh_user: padmin` and escalate to root via sudo
- SSH key authentication via `id_rsa` file (fetched by `prepare.yml`)
- DNS managed via API at `https://run-tools.homecredit.vn/dnsphpadmin/api.php`
- Secrets are managed through Ansible Vault (DNS token, SSH keys)
- Production environment with Oracle Linux servers
- Global data center setting: `vn_pdc`
- Proxy filtering uses multiple ACL files in `files/` directory
- Target hosts can be overridden using `-e target_hosts=group_name`
- VLAN blocking uses external files for flexibility - no config file edits needed