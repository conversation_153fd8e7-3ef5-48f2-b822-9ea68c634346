---
# Refactored Squid Installation and Configuration Tasks

- name: Install squid package
  yum:
    name: squid
    state: present

- name: Create squid data directories
  file:
    path: "{{ item }}"
    state: directory
    owner: squid
    group: squid
    mode: '0755'
  loop:
    - /DATA/squid-logs
    - /DATA/cache
    - /DATA/cache1
    - /DATA/cache2
    - /DATA/cache3

- name: Upload ACL and filter files
  copy:
    src: "{{ item }}"
    dest: "/etc/squid/{{ item }}"
    mode: '0644'
    owner: root
    group: root
  loop:
    - server_networks
    - ads_blocked_urls
    - atp_block_domains
    - atp_block_ip
    - blacklist_urls
    - block_src
    - whitelist_urls
    - allow_src
  notify: validate and restart squid

- name: Generate squid configuration from template
  template:
    src: squid.conf.j2
    dest: /etc/squid/squid.conf
    mode: '0644'
    owner: root
    group: root
    backup: yes
  notify: validate and restart squid

- name: Ensure squid service is enabled
  systemd:
    name: squid
    enabled: yes
    daemon_reload: yes