---
# Squid Service Handlers

- name: validate and restart squid
  block:
    - name: Validate squid configuration syntax
      command: squid -k parse
      register: squid_syntax_check
      failed_when: squid_syntax_check.rc != 0
      changed_when: false

    - name: Restart squid service
      systemd:
        name: squid
        state: restarted
        enabled: yes
      when: squid_syntax_check.rc == 0

- name: reload squid
  systemd:
    name: squid
    state: reloaded
  when: squid_syntax_check is defined and squid_syntax_check.rc == 0