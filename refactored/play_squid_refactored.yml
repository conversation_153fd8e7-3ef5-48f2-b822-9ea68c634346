---
- name: Deploy Refactored Squid Proxy Configuration
  hosts: squid-pdc,squid-drc
  become: yes
  gather_facts: yes

  vars:
    # Load common variables first
    squid_config_base: "{{ playbook_dir }}/refactored"

  pre_tasks:
    - name: Load common squid variables
      include_vars: "{{ squid_config_base }}/group_vars/squid-common.yml"

    - name: Load datacenter-specific variables
      include_vars: "{{ squid_config_base }}/group_vars/squid-{{ 'pdc' if inventory_hostname in groups['squid-pdc'] else 'drc' }}.yml"

  tasks:
    - name: Include squid configuration tasks
      include_tasks: "{{ squid_config_base }}/tasks/squid.yml"

  handlers:
    - name: Include squid handlers
      include: "{{ squid_config_base }}/handlers/squid.yml"

  post_tasks:
    - name: Verify squid is running
      systemd:
        name: squid
        state: started
      register: squid_status

    - name: Display squid status
      debug:
        msg: "Squid service is {{ squid_status.status.ActiveState }} on {{ ansible_hostname }}"