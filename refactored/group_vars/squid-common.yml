# Common Squid Configuration Variables
# Shared across all squid instances

# Basic Settings
squid_visible_hostname: 'proxy-server.homecredit.vn'
squid_http_port: 8080
squid_http_port_options: 'require-proxy-header'
squid_via: 'on'

# DNS Configuration
squid_dns_nameservers:
  - '***********'
  - '***********'
  - '***********'
  - '***********'

# Memory and Size Limits
squid_maximum_object_size: '8 MB'
squid_request_header_max_size: '64 KB'

# Cache Settings
squid_ipcache_size: 10072
squid_ipcache_low: 90
squid_ipcache_high: 95
squid_fqdncache_size: 10072
squid_peer_connect_timeout: '30 seconds'
squid_half_closed_clients: 'off'

# Logging
squid_log_mime_hdrs: 'on'
squid_logformat: '%tl.%03tu %6tr %>a %Ss/%03>Hs %<st %rm %ru %[un %Sh/%<a %mt "%{User-Agent}>h"'
squid_access_logs:
  - path: '/DATA/squid-logs/access.log'
    format: 'squid1'
    acl: 'hasRequest'
  - path: 'syslog:authpriv.info'
    format: 'squid1'
    acl: 'hasRequest'
squid_cache_log: '/DATA/squid-logs/cache.log squid1'
squid_cache_store_log: '/DATA/squid-logs/store.log squid1'

# Performance
squid_quick_abort_min: '-1 KB'
squid_negative_ttl: 0

# Safe Ports (grouped for readability)
squid_safe_ports_groups:
  - ports: [22]
    comment: 'ssh'
  - ports: [53]
    comment: 'dns'
  - ports: [80, 81]
    comment: 'http'
  - ports: [21]
    comment: 'ftp'
  - ports: [443]
    comment: 'https'
  - ports: [70]
    comment: 'gopher'
  - ports: [210]
    comment: 'wais'
  - ports: [44329, 5228]
    comment: 'special services'
  - ports: ['1025-65535']
    comment: 'unregistered ports'
  - ports: [280, 488, 591, 777, 8443]
    comment: 'management and misc'

# Content Filtering ACLs
squid_filter_acls:
  whitelist_urls:
    type: 'dstdom_regex'
    file: 'whitelist_urls'
  block_ads:
    type: 'dstdom_regex'
    file: 'ads_blocked_urls'
  atp_bll_domain:
    type: 'dstdomain'
    file: 'atp_block_domains'
  atp_bll_ip:
    type: 'dst'
    file: 'atp_block_ip'
  block_src:
    type: 'src'
    file: 'block_src'
  hasRequest:
    type: 'has'
    file: 'request'

# Deny Rules (in order)
squid_deny_rules:
  - 'block_src'
  - 'block_ads'
  - 'atp_bll_domain'
  - 'atp_bll_ip'

# Refresh Patterns
squid_refresh_patterns:
  - regex: '^ftp:'
    min: 1440
    percent: 20
    max: 10080
  - regex: '^gopher:'
    min: 1440
    percent: 0
    max: 1440
  - regex: '-i \.(gif|png|jpg|jpeg|ico)$'
    min: 10080
    percent: 90
    max: 43200
    options: 'override-expire ignore-no-cache ignore-private'
  - regex: '-i \.(iso|avi|wav|mp3|mp4|mpeg|swf|flv|x-flv)$'
    min: 43200
    percent: 90
    max: 432000
    options: 'override-expire ignore-no-cache ignore-private'
  - regex: '-i \.(deb|rpm|exe|zip|tar|tgz|ram|rar|bin|ppt|doc|tiff)$'
    min: 10080
    percent: 90
    max: 43200
    options: 'override-expire ignore-no-cache ignore-private'
  - regex: '-i \.index.(html|htm)$'
    min: 0
    percent: 40
    max: 10080
  - regex: '-i \.(html|htm|css|js)$'
    min: 1440
    percent: 40
    max: 40320
  - regex: '.'
    min: 0
    percent: 40
    max: 40320

# Administrative
squid_cache_manager: '<EMAIL>'
squid_max_filedesc: 30720
squid_shutdown_lifetime: '0 seconds'
squid_forwarded_for: 'on'