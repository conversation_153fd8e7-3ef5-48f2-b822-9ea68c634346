# Squid Proxy Configuration
# Generated for: {{ ansible_hostname }}
# Data Center: {{ squid_datacenter }}
# Environment: {{ ansible_environment | default('production') }}

# Basic Configuration
visible_hostname '{{ squid_visible_hostname }}'
http_port {{ squid_http_port }} {{ squid_http_port_options }}
via {{ squid_via }}

# DNS Configuration
dns_nameservers {{ squid_dns_nameservers | join(' ') }}

# Memory and Object Size Limits
maximum_object_size {{ squid_maximum_object_size }}
request_header_max_size {{ squid_request_header_max_size }}

# Cache Directories
{% for cache_dir in squid_cache_dirs %}
cache_dir {{ cache_dir.type }} {{ cache_dir.path }} {{ cache_dir.size }} {{ cache_dir.l1 }} {{ cache_dir.l2 }} {{ cache_dir.options | default('') }}
{% endfor %}

# Cache Settings
ipcache_size {{ squid_ipcache_size }}
ipcache_low {{ squid_ipcache_low }}
ipcache_high {{ squid_ipcache_high }}
fqdncache_size {{ squid_fqdncache_size }}
peer_connect_timeout {{ squid_peer_connect_timeout }}
half_closed_clients {{ squid_half_closed_clients }}

# Logging Configuration
log_mime_hdrs {{ squid_log_mime_hdrs }}
logformat squid1 {{ squid_logformat }}
{% for log in squid_access_logs %}
access_log {{ log.path }} {{ log.format }} {{ log.acl | default('') }}
{% endfor %}
cache_log {{ squid_cache_log }}
cache_store_log {{ squid_cache_store_log }}

# Refresh Patterns
{% for pattern in squid_refresh_patterns %}
refresh_pattern {{ pattern.regex }} {{ pattern.min }} {{ pattern.percent }}% {{ pattern.max }} {{ pattern.options | default('') }}
{% endfor %}

# Performance Settings
quick_abort_min {{ squid_quick_abort_min }}
negative_ttl {{ squid_negative_ttl }}

# ACL Definitions
acl PURGE method PURGE
acl CONNECT method CONNECT
acl manager proto cache_object

# Network ACLs
acl server_network src "/etc/squid/server_networks"
acl localnetwork src {{ squid_local_network }}
{% if squid_haproxy_ips is defined %}
acl haproxy src {{ squid_haproxy_ips | join(' ') }} 127.0.0.1
{% endif %}

# Port ACLs
{% for port_group in squid_safe_ports_groups %}
acl Safe_ports port {{ port_group.ports | join(' ') }}  # {{ port_group.comment }}
{% endfor %}

# Content Filtering ACLs
{% for acl_name, acl_config in squid_filter_acls.items() %}
acl {{ acl_name }} {{ acl_config.type }} "/etc/squid/{{ acl_config.file }}"
{% endfor %}
acl QUERY urlpath_regex cgi-bin \?

# Cache Control
cache deny QUERY
cache deny all

# Proxy Protocol and Forwarding
{% if squid_haproxy_ips is defined %}
follow_x_forwarded_for allow haproxy
{% endif %}
proxy_protocol_access allow all
forwarded_for {{ squid_forwarded_for }}

# HTTP Access Rules (Order matters!)
# 1. Deny unsafe ports
http_access deny !Safe_ports

# 2. Deny blocked content and sources
{% for deny_rule in squid_deny_rules %}
http_access deny {{ deny_rule }}
{% endfor %}

# 3. Allow management access
http_access allow manager localhost
http_access allow manager localnetwork
http_access allow PURGE localhost

# 4. Allow legitimate network traffic
http_access allow CONNECT server_network
http_access allow CONNECT localnetwork
http_access allow server_network
http_access allow localnetwork

# 5. Deny non-server networks
http_access deny !server_network

# 6. Deny unauthorized manager access
http_access deny manager

# 7. Final allow (should rarely be reached)
http_access allow all

# Reply and ICP Access
http_reply_access allow all
icp_access allow all

# Administrative Settings
cache_mgr {{ squid_cache_manager }}
max_filedesc {{ squid_max_filedesc }}
shutdown_lifetime {{ squid_shutdown_lifetime }}