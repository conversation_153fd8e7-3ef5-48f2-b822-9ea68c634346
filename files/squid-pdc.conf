# Squid listens port
visible_hostname 'proxy-server.homecredit.vn'
#http_port 8080
http_port 8080 require-proxy-header
via on

dns_nameservers *********** *********** *********** ***********
maximum_object_size 8 MB
request_header_max_size 64 KB

cache_dir diskd /DATA/cache 12600 29 256 Q1=100 Q2=120
cache_dir diskd /DATA/cache1 12600 29 256 Q1=100 Q2=120
cache_dir diskd /DATA/cache2 12600 29 256 Q1=100 Q2=120
ipcache_size 10072
ipcache_low 90
ipcache_high 95
fqdncache_size 10072
peer_connect_timeout 30 seconds
half_closed_clients off
log_mime_hdrs on
#acl for log
acl hasRequest has request
logformat squid1 %tl.%03tu %6tr %>a %Ss/%03>Hs %<st %rm %ru %[un %Sh/%<a %mt "%{User-Agent}>h"
access_log /DATA/squid-logs/access.log squid1 hasRequest
access_log syslog:authpriv.info squid1 hasRequest
cache_log /DATA/squid-logs/cache.log squid1
cache_store_log /DATA/squid-logs/store.log squid1

refresh_pattern ^ftp:           1440    20%     10080
refresh_pattern ^gopher:        1440    0%      1440

refresh_pattern -i \.(gif|png|jpg|jpeg|ico)$ 10080 90% 43200 override-expire ignore-no-cache ignore-private
refresh_pattern -i \.(iso|avi|wav|mp3|mp4|mpeg|swf|flv|x-flv)$ 43200 90% 432000 override-expire ignore-no-cache ignore-private
refresh_pattern -i \.(deb|rpm|exe|zip|tar|tgz|ram|rar|bin|ppt|doc|tiff)$ 10080 90% 43200 override-expire ignore-no-cache ignore-private
refresh_pattern -i \.index.(html|htm)$ 0 40% 10080
refresh_pattern -i \.(html|htm|css|js)$ 1440 40% 40320
refresh_pattern . 0 40% 40320

quick_abort_min -1 KB

negative_ttl 0
acl PURGE method PURGE
acl manager proto cache_object
#acl localhost src "/etc/squid/iplocal"
acl server_network src ***********/24 ***********/24 ************/24 ************** ************** ************/24 ***********/24 ***********/24 ***********/24 ***********/24 **************/25 ************/24 ************/24 ***********/24 ***********/24 ***********/24 ***********/24 ***********/24 ***********/24 ***********/24 ***********/24 ***********/24 ************/24 ***********/24 ***********/24 ***********/24 **********/24 ***********/24 ***********/24 ************* ***********/24 **********/24 ************* ***********/24 ***********/24 ***********/23 ***********/23 ***********/24 ***********/24 ***********/24 ***********/24 ************** ************ ************* *********** ***********/24 ***********/24 ***********/24 **********/22 **********/22 **********/24 **********/24 ***********/24 *********/16 ************* *********** ************ *********** *********** ***********/24 ***********/24 **********/24 ***********/24 *********** ***********/24 ***********/24 10.19.112.0/24 10.16.87.0/24 10.16.81.120 10.19.206.69/32 10.19.206.48/32 172.24.125.153 172.24.125.216 10.19.20.0/24 10.19.210.0/24 10.19.192.0/24 10.19.134.0/24 172.24.66.32 10.23.186.0/24 10.19.166.0/24 10.19.24.0/24 10.19.40.0/24 172.24.114.0/24 10.18.162.0/24 10.17.205.0/24 10.22.191.0/24 10.19.164.0/24
acl localnetwork src **********/24
acl Safe_ports port 22		# ssh
acl Safe_ports port 53		# dns*
acl Safe_ports port 80          # http
acl Safe_ports port 81          # http
acl Safe_ports port 21          # ftp
acl Safe_ports port 443         # https
acl Safe_ports port 70          # gopher
acl Safe_ports port 210         # wais
acl Safe_ports port 44329
acl Safe_ports port 5228	#ggads
acl Safe_ports port 1025-65535  # unregistered ports
acl Safe_ports port 280         # http-mgmt
acl Safe_ports port 488         # gss-http
acl Safe_ports port 591         # filemaker
acl Safe_ports port 777         # multiling http
acl Safe_ports port 8443
acl CONNECT method CONNECT


# acl
acl whitelist_urls dstdom_regex "/etc/squid/whitelist_urls"
acl block_ads dstdom_regex "/etc/squid/ads_blocked_urls"
acl atp_bll_domain dstdomain "/etc/squid/atp_block_domains"
acl atp_bll_ip dst "/etc/squid/atp_block_ip"
acl CONNECT method CONNECT
acl block_src src "/etc/squid/block_src"
#acl blocked_networks src "/etc/squid/blocked_networks"
#acl allowed_networks src "/etc/squid/allowed_networks"
#acl allow_src src "/etc/squid/allow_src"
acl QUERY urlpath_regex cgi-bin \?
cache deny QUERY
#haproxy 
acl haproxy src *********** *********** *********** 127.0.0.1
follow_x_forwarded_for allow haproxy
#follow_x_forwarded_for	--enable-follow-x-forwarded-for
proxy_protocol_access allow all
forwarded_for on

cache deny all
#http_access allow allow_src
#http_access allow allowed_networks
#http_access deny blocked_networks
http_access deny block_src
http_access deny block_ads
http_access deny atp_bll_domain
http_access deny atp_bll_ip
http_access deny !Safe_ports
http_access allow manager localhost
http_access allow manager localnetwork
http_access allow PURGE localhost
http_access allow CONNECT server_network
http_access allow CONNECT localnetwork
http_access allow server_network
http_access allow localnetwork
http_access allow CONNECT server_network
http_access deny manager
http_access deny !server_network
http_access allow all
http_reply_access allow all
icp_access allow all
cache_mgr <EMAIL>
max_filedesc 30720
shutdown_lifetime 0 seconds