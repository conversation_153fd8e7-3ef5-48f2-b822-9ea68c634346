 ! Configuration File for keepalived
global_defs {
        script_user root
}
vrrp_script chk_service {           # Requires keepalived-1.1.13
        script "/etc/keepalived/check_script.sh"     # cheaper than pidof
        interval 2                      # check every 2 seconds
}
vrrp_instance VI_1 {
    state MASTER
    interface eth0
    virtual_router_id 70
        priority 100
        advert_int 3
    authentication {
        auth_type PASS
        auth_pass 1111
    }
    virtual_ipaddress {
        ***********/32 dev eth0
            }
    track_script {
        chk_service
    }
}
