#drc not require-proxy-header 
visible_hostname proxy-sever.homecredit.vn
http_port 8080
#http_port 8080 require-proxy-header
acl localnet src ***********/24 **********/24 ************/24 ************** ************** ************/24 ***********/24 ***********/24 ***********/24 ***********/24 **************/25 ************/24 ************/24 ***********/24 ***********/24 ***********/24 ***********/24 ***********/24 ***********/24 ***********/24 ***********/24 ***********/24 ************/24 ***********/24 ***********/24 ***********/24 **********/24 ***********/24 ***********/24 ************* ***********/24 **********/24 ************* ***********/24 ***********/24 ***********/23 ***********/23 ***********/24 ***********/24 ***********/24 ***********/24 ************** ************ ************* *********** ***********/24 ***********/24 ***********/24 **********/22 **********/22 **********/24 **********/24 ***********/24 *********/16 ************* *********** ************ *********** *********** ***********/24 ***********/24 10.22.16.0/24 10.19.149.0/24 10.16.80.11 10.22.189.0/24 10.22.190.0/24 10.19.112.0/24 10.16.87.0/24 10.16.81.120 10.19.206.69/32 172.24.125.153 172.24.125.216 10.19.20.0/24 10.19.210.0/24 10.19.192.0/24 10.19.134.0/24 172.24.66.32 10.23.186.0/24 10.19.166.0/24 10.19.24.0/24 10.19.40.0/24 172.24.114.0/24 10.18.162.0/24 10.17.205.0/24 10.22.191.0/24 10.19.164.0/24 10.23.190.0/24



acl SSL_ports port 443
acl SSL_ports port 3000
acl SSL_ports port 22


acl Safe_ports port 23
acl Safe_ports port 3000
acl Safe_ports port 80
acl Safe_ports port 21
acl Safe_ports port 443
acl Safe_ports port 70
acl Safe_ports port 210
acl Safe_ports port 1025-65535
acl Safe_ports port 280
acl Safe_ports port 488
acl Safe_ports port 591
acl Safe_ports port 777

acl whitelist_urls dstdom_regex "/etc/squid/whitelist_urls"
acl block_ads dstdom_regex "/etc/squid/ads_blocked_urls"
acl atp_bll_domain dstdomain "/etc/squid/atp_block_domains"
acl atp_bll_ip dst "/etc/squid/atp_block_ip"
acl CONNECT method CONNECT
acl QUERY urlpath_regex cgi-bin \?
acl block_src src "/etc/squid/block_src"
acl allow_src src "/etc/squid/allow_src"
acl hasRequest has request
cache_mem 512 MB
icp_port 3130
memory_replacement_policy heap GDSF
cache_replacement_policy heap LFUDA
ipcache_size 10072
ipcache_low 90
ipcache_high 95
fqdncache_size 10072
half_closed_clients off
buffered_logs off
pipeline_prefetch on
memory_pools off
cache deny QUERY
coredump_dir /var/spool/squid
dns_nameservers *********** *********** *********** ***********
negative_ttl 3 minutes
http_reply_access allow all
icp_access allow all
persistent_connection_after_error on
max_filedesc 30720
#request_body_max_size 100 MB all
#reply_body_max_size 100 MB all

request_body_max_size 10000 MB all
reply_body_max_size 10000 MB all

logformat squid1 %tl.%03tu %6tr %>a %Ss/%03>Hs %<st %rm %ru %[un %Sh/%<a %mt "%{User-Agent}>h"
access_log /DATA/squid-logs/access.log squid1 hasRequest
access_log syslog:authpriv.info squid1 hasRequest
cache_log /DATA/squid-logs/cache.log squid1
cache_store_log /DATA/squid-logs/store.log squid1


http_access allow whitelist_urls
#http_access allow allow_src
http_access deny block_src
http_access deny block_ads
http_access deny atp_bll_domain
http_access deny atp_bll_ip
http_access deny !Safe_ports
http_access allow localnet
http_access allow CONNECT localnet
http_access deny CONNECT !SSL_ports
http_access allow localhost manager
http_access deny manager
http_access allow localhost
http_access deny all

cache_dir diskd /DATA/cache 12600 29 256 Q1=100 Q2=120
cache_dir diskd /DATA/cache1 12600 29 256 Q1=100 Q2=120
cache_dir diskd /DATA/cache2 12600 29 256 Q1=100 Q2=120

refresh_pattern  ^ftp: 1440 20% 10080 
refresh_pattern  ^gopher: 1440 10% 1440 
refresh_pattern -i (/cgi-bin/|\?): 0 0% 0 
refresh_pattern  . 0 20% 4320 
refresh_pattern -i \.(jp(e?g|e|2)|gif|pn[pg]|bm?|tiff?|ico|swf|css|js) 129600 100% 129600 
refresh_pattern -i \.(iso|avi|wav|mp3|mp4|mpeg|swf|flv|x-flv)$  43200 90% 43200 
refresh_pattern -i \.(deb|rpm|exe|zip|tar|tgz|ram|rar|bin|ppt|doc|tiff)$ 10080 90% 43200 
refresh_pattern -i \.index.(html|htm)$ 0 40% 10080 
refresh_pattern -i \.(html|htm|css|js)$ 1440 40% 40320 
refresh_pattern -i . 0 40% 40320 
refresh_pattern -i \.symantecliveupdate\.com\/.*\.(zip|7z|irn|[m|x][0-9][0-9]) 4320 100% 43200 reload-into-ims
refresh_pattern -i .*dnl.*\.geo\.kaspersky\.(com|ru)\/.*\.(zip|avc|kdc|nhg|klz|d[at|if]) 4320 100% 43200 reload-into-ims
refresh_pattern -i \.kaspersky-labs\.(com|ru)\/.*\.(cab|zip|exe|ms[i|p]) 4320 100% 43200 reload-into-ims
refresh_pattern -i \.kaspersky\.(com|ru)\/.*\.(cab|zip|exe|ms[i|p]|avc) 4320 100% 43200 reload-into-ims
refresh_pattern -i .update\.geo\.drweb\.com 4320 100% 43200 reload-into-ims
refresh_pattern -i \.avast.com\/.*\.(vp[u|aa]) 4320 100% 43200 reload-into-ims
refresh_pattern -i \.avg.com\/.*\.(bin) 4320 100% 43200 reload-into-ims


http_reply_access allow all
icp_access allow all
cache_mgr <EMAIL>
max_filedesc 30720
shutdown_lifetime 0 seconds


## optimize -> error assertion failed: client_side.cc:1577: "EX"
pipeline_prefetch off
client_persistent_connections off
server_persistent_connections off