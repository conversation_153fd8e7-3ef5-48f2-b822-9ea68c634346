- name: Play add user qualys for all servers
  hosts: allserver
  gather_facts: false

  vars:
    global_repo_env: prod
    disable_os_repos: yes
    # ansible_python_interpreter: /usr/bin/python2.4

  tasks:
  # - name: Add user qualys for Affinity hosts
  #   block:
  #   - name: Add user qualys
  #     include_role:
  #       name: security_hcvn
  #     tags: 
  #       - qualys-user
  #   when: inventory_hostname in groups['Affinity_prod']  

  # - name: Add user qualys for PCI-DSS hosts
  #   block:
  #   - name: Add user qualys
  #     include_role:
  #       name: security_hcvn
  #     tags: 
  #       - qualys-user
  #   when: inventory_hostname in groups['pcidss_prod']  

  - name: Add user qualys for linux-app-prod hosts
    block:
    - name: Add user qualys
      include_role:
        name: create_qualys_account
      #tags: 
        #- qualys-user
    #when: (inventory_hostname in groups['linux-app-prod']) and (inventory_hostname == 'vnhqapp07')

    
      
