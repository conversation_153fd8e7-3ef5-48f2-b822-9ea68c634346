- name: Configuration forward the security log on server to Qradar
  hosts: proxy
  gather_facts: false

  # vars:
  #   global_repo_env: prod
  #   disable_os_repos: yes
    # ansible_python_interpreter: /usr/bin/python2.4

  tasks:
  - name: Configuration forward the security log on server to Qradar
    block:
    # - debug:
    #     msg: "{{ inventory_hostname }} in group qradar_log"
    - name: Config syslog to server qradar
      include_role:
        name: deploy-qradar-syslog
    when: inventory_hostname in groups['security']  