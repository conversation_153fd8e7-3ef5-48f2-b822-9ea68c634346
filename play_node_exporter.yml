- name:           Install Node_exporter
  hosts:          proxy
  gather_facts:   true
  environment:
    # http_proxy: "{{ proxy_host }}"
    # https_proxy: "{{ proxy_host }}"
    no_proxy: "*"    
  tasks:
    - name: Install unzip
      yum:
        name:
          - unzip     
    - name: node_exporter
      include_role:
        name: node_exporter

    # #copy user.sh /opt
    # - name: user.sh
    #   copy:
    #     src: "user.sh"
    #     dest: "/opt/user.sh"
    #     mode: 0755
    #     owner: root
    #     group: root
    # #Create folder to Node_exporter get metric from file
    # - name: create node_exporter_textfile   
    #   file:
    #     path: /var/lib/node_exporter/
    #     state: directory
    #     owner: root
    #     group: root
    #     mode: '0755' 
    #   notify:
    #   - Restart node          
    # # set crontab to get metric    
    # - name: crontab
    #   cron:
    #     name: Sets cron job to run script user.sh
    #     minute: "*"
    #     hour: "*"
    #     day: "*"
    #     month: "*"
    #     weekday: "*"
    #     user: root
    #     job: "/bin/bash /opt/user.sh >/var/lib/node_exporter/user.prom.new && mv /var/lib/node_exporter/user.prom.new /var/lib/node_exporter/user.prom"
    #     cron_file: ansible_push
    #   notify:
    #   - Restart cron   

    # - name: crontab1
    #   cron:
    #     name: Sets cron job to run script push gateway 10
    #     minute: "*"
    #     hour: "*"
    #     day: "*"
    #     month: "*"
    #     weekday: "*"
    #     user: root
    #     job: "sleep 10 ; /bin/bash /opt/user.sh >/var/lib/node_exporter/user.prom.new && mv /var/lib/node_exporter/user.prom.new /var/lib/node_exporter/user.prom"
    #     cron_file: ansible_push1
    #   notify:
    #   - Restart cron  

    # - name: crontab2
    #   cron:
    #     name: Sets cron job to run script push gateway 20
    #     minute: "*"
    #     hour: "*"
    #     day: "*"
    #     month: "*"
    #     weekday: "*"
    #     user: root
    #     job: "sleep 20 ; /bin/bash /opt/user.sh"
    #     cron_file: ansible_push2
    #   notify:
    #   - Restart cron    

    # - name: crontab3
    #   cron:
    #     name: Sets cron job to run script push gateway 30
    #     minute: "*"
    #     hour: "*"
    #     day: "*"
    #     month: "*"
    #     weekday: "*"
    #     user: root
    #     job: "sleep 30 ; /bin/bash /opt/user.sh >/var/lib/node_exporter/user.prom.new && mv /var/lib/node_exporter/user.prom.new /var/lib/node_exporter/user.prom"
    #     cron_file: ansible_push3
    #   notify:
    #   - Restart cron

    # - name: crontab4
    #   cron:
    #     name: Sets cron job to run script push gateway 40
    #     minute: "*"
    #     hour: "*"
    #     day: "*"
    #     month: "*"
    #     weekday: "*"
    #     user: root
    #     job: "sleep 40 ; /bin/bash /opt/user.sh >/var/lib/node_exporter/user.prom.new && mv /var/lib/node_exporter/user.prom.new /var/lib/node_exporter/user.prom"
    #     cron_file: ansible_push4
    #   notify:
    #   - Restart cron

    # - name: crontab5
    #   cron:
    #     name: Sets cron job to run script push gateway 50
    #     minute: "*"
    #     hour: "*"
    #     day: "*"
    #     month: "*"
    #     weekday: "*"
    #     user: root
    #     job: "sleep 50 ; /bin/bash /opt/user.sh >/var/lib/node_exporter/user.prom.new && mv /var/lib/node_exporter/user.prom.new /var/lib/node_exporter/user.prom"
    #     cron_file: ansible_push5
    #   notify:
    #   - Restart cron

    # - name: clear cache
    #   cron:
    #     name: Sets cron job to clear cache
    #     minute: "30"
    #     hour: "1"
    #     day: "*"
    #     month: "*"
    #     weekday: "*"
    #     user: root
    #     job: echo 1 > /proc/sys/vm/drop_caches
    #     cron_file: ansible_dropcache
    #   notify:
    #   - Restart cron

  handlers:
    # - name: Restart cron
    #   systemd:
    #     name: crond
    #     state: restarted
    #     daemon_reload: yes
    - name: Restart node
      systemd:
        name: node_exporter
        state: restarted
        daemon_reload: yes            