stages:
  - tf_validate
  - tf_plan
  - tf_apply
  - ans_os_baseline   
  - ans_validate
  - ans_dns_install
  - ans_play_haproxy
  - ans_play_squid
  - ans_node_exporter
  - ans_log_splunk
  - ans_qualys
  - ans_promtail


image:
  # name: registry.vn.eit.zone/platform_vn/devops-ci-tools:v0.8.2
  name: registry.vn.eit.zone/platform_vn/devops-ci-tools:v0.4
  entrypoint:
    - "/usr/bin/env"
    - "PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
    - "http_proxy=http://proxy-server.homecredit.vn:8080"
    - "https_proxy=http://proxy-server.homecredit.vn:8080"
    - "no_proxy=vnpdcvct02.pdc.hcnet.vn:9443,vnpdcvct02.pdc.hcnet.vn,************,vcenter-hcm.vn.prod,vcenter-bnd.vn.prod,repo-vn.vn.prod,vnpdc-foreman.vn.prod"

before_script:
    - if [ -z ${RUN_ENV+x} ]; then export RUN_ENV='prod'; else echo "Runing on environment $RUN_ENV"; fi
    # - echo "$SSH_PRIVATE_KEY" > id_rsa
    # - chmod 600 id_rsa
    - export ANSIBLE_HOST_KEY_CHECKING=False
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - chmod o-w .
    - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
    - envsubst < requirements.tmpl > requirements.yml
    - ansible-galaxy install -r requirements.yml --ignore-errors
    # - ansible-playbook --syntax-check -i inventory play*.yml
    - ansible-playbook -i inventory.yml prepare.yml

# Default output file for Terraform plan
variables:
  ANSIBLE_FORCE_COLOR: 'true'
  ANSIBLE_STOUT_CALLBACK: 'debug'
  PLAN: plan.tfplan
  JSON_PLAN_FILE: tfplan.json
  # Allow git interactions (e. g. lerna version & publish)
  GIT_AUTHOR_NAME: $GITLAB_USER_NAME
  GIT_AUTHOR_EMAIL: $GITLAB_USER_EMAIL
  GIT_COMMITTER_NAME: $GITLAB_USER_NAME
  GIT_COMMITTER_EMAIL: $GITLAB_USER_EMAIL
  RUN_ENV: prod
  global_gitlab_token: "{{ lookup('env', 'GITLAB_TOKEN') }}"
  run_env: "{{ lookup('env', 'RUN_ENV') | default('prod', true) }}"

  SKIP_TF: "TRUE"
  SKIP_ANSIBLE_VALIDATE: ""
  SKIP_DNS: ""
  SKIP_OS_BASELINE: ""
  SKIP_HAPROXY: ""
  SKIP_SQUID: ""
  SKIP_NODE_EXPORTER: ""
  SKIP_LOG_SPLUNK: ""
  TF_ONLY: ""
  SKIP_QUALYS: ""
  SKIP_PROMTAIL: ""

cache:
  paths:
    - .terraform

#########################################
.general: &general
  when: manual
  only:
    - master
    - staging
  tags: ["ansible", "infra", "itops", "system"]

.ansible_job: &ansible_job
  <<: *general
  image:
    name: registry.vn.eit.zone/platform_vn/devops-ci-tools:v0.4
    entrypoint:
    - "/usr/bin/env"
    - "PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
    - "http_proxy=http://proxy-server.homecredit.vn:8080"
    - "https_proxy=http://proxy-server.homecredit.vn:8080"
    - "no_proxy=vnpdcvct02.pdc.hcnet.vn:9443,vnpdcvct02.pdc.hcnet.vn,*.prod,*.nonprod,*.zone,repo-vn.vn.prod,git.homecredit.net,************,run-tools.homecredit.vn,*************:5665,ipplan.cz.prod"
  before_script:
    - export ANSIBLE_HOST_KEY_CHECKING=False
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
    - apk add zip tar
    - envsubst < requirements.tmpl > requirements.yml
    - ansible-galaxy install -r requirements.yml --ignore-errors
    - ansible-playbook -i inventory.yml prepare.yml

.terraform_job: &terraform_job
  <<: *general
  before_script:
    - export ANSIBLE_HOST_KEY_CHECKING=False
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
    - envsubst < requirements.tmpl > requirements.yml
    - ansible-galaxy install -r requirements.yml --ignore-errors
    - ansible-playbook -i inventory.yml prepare.yml
    - cp -f ./id_rsa ~/.ssh/
    - shopt -s expand_aliases
    - alias convert_report="jq -r '([.resource_changes[].change.actions?]|flatten)|{\"create\":(map(select(.==\"create\"))|length),\"update\":(map(select(.==\"update\"))|length),\"delete\":(map(select(.==\"delete\"))|length)}'"
    - cd terraform
    - terraform --version
    - terraform init

.ansible_job_alt_proxy: &ansible_job_alt_proxy
  <<: *general
  image:
    name: registry.vn.eit.zone/platform_vn/devops-ci-tools:v0.4
    entrypoint:
    - "/usr/bin/env"
    - "PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
    - "http_proxy=http://************:8080"
    - "https_proxy=http://************:8080"
    - "no_proxy=vnpdcvct02.pdc.hcnet.vn:9443,vnpdcvct02.pdc.hcnet.vn,*.prod,*.nonprod,*.zone,repo-vn.vn.prod,git.homecredit.net,************,run-tools.homecredit.vn,*************:5665,ipplan.cz.prod"
  before_script:
    - if [ -z ${RUN_ENV+x} ]; then export RUN_ENV='prod'; else echo "Runing on environment $RUN_ENV"; fi
    - export ANSIBLE_HOST_KEY_CHECKING=False
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
    - envsubst < requirements.tmpl > requirements.yml
    - ansible-galaxy install -r requirements.yml --ignore-errors
    - ansible-playbook -i inventory.yml prepare.yml

#########################################  


tf_validate:
  <<: *terraform_job
  stage: tf_validate
  script:
    - terraform validate

tf_plan:
  <<: *terraform_job
  stage: tf_plan
  script:
    - unset https_proxy
    - terraform plan -out=$PLAN
    - "terraform show --json $PLAN | convert_report > $JSON_PLAN_FILE"
  artifacts:
    name: plan
    paths:
      - terraform/$PLAN
    reports:
      terraform: terraform/$JSON_PLAN_FILE
tf_apply:
  <<: *terraform_job
  stage: tf_apply
  environment:
    name: production
  script:
    - unset https_proxy
    - terraform apply -input=false $PLAN

ans_validate:
  <<: *general
  stage: ans_validate
  script:
    - ls -la
    - ansible-playbook --syntax-check -i inventory.yml play*.yml 

dns_setup:
  <<: *general
  stage: ans_dns_install
  script:
    # - ansible-playbook -i inventory.yml --vault-id ${RUN_ENV}@ansible_vault_password play_install_sentinelone.yml --tags "prod_vms"
    - ansible-playbook -i inventory.yml --vault-id ${RUN_ENV}@ansible_vault_password play_dns.yml --tags "dns_setup"
  # except:
  #   variables:
  #     - $SKIP_DNS
  # when: manual
  # only:
  #   - master
  # tags: ["ansible", "infra", "itops","system"]

os_baseline:
  <<: *general
  stage: ans_os_baseline
  script:
    - pip install netaddr
    - unset http_proxy
    - unset https_proxy
    - unset no_proxy
    # - ansible-playbook -i inventory.yml --vault-id ${RUN_ENV}@ansible_vault_password play_install_sentinelone.yml --tags "prod_vms"
    - ansible-playbook -i inventory.yml --vault-id ${RUN_ENV}@ansible_vault_password play_os_baseline.yml
  # except:
  #   variables:
  #     - $SKIP_OS_BASELINE
  # when: manual
  # only:
  #   - master
  # tags: ["ansible", "infra", "itops","system"]

ans_play_haproxy:
  <<: *ansible_job
  stage: ans_play_haproxy
  script:
    - "ansible-playbook -i inventory.yml --vault-id prod@ansible_vault_password play_haproxy.yml"
  except:
    variables:
      - $SKIP_HAPROXY 


ans_play_squid_drc:
  <<: *ansible_job
  stage: ans_play_squid
  script:
    - "ansible-playbook -i inventory.yml --vault-id prod@ansible_vault_password -e target_hosts=squid-drc play_squid.yml"
  except:
    variables:
      - $SKIP_SQUID

ans_play_squid_pdc:
  <<: *ansible_job
  stage: ans_play_squid
  script:
    - "ansible-playbook -i inventory.yml --vault-id prod@ansible_vault_password -e target_hosts=squid-pdc play_squid.yml"
  except:
    variables:
      - $SKIP_SQUID

ans_play_squid_all:
  <<: *ansible_job
  stage: ans_play_squid
  script:
    - "ansible-playbook -i inventory.yml --vault-id prod@ansible_vault_password -e target_hosts=proxy play_squid.yml"
  except:
    variables:
      - $SKIP_SQUID

 

ans_node_exporter:
  <<: *ansible_job
  stage: ans_node_exporter
  script:
    - "ansible-playbook -i inventory.yml --vault-id prod@ansible_vault_password play_node_exporter.yml"
  except:
    variables:
      - $SKIP_NODE_EXPORTER

ans_log_splunk:
  <<: *ansible_job_alt_proxy
  stage: ans_log_splunk
  script:
    - "ansible-playbook -i inventory.yml --vault-id prod@ansible_vault_password play_log_splunk.yml"
  except:
    variables:
      - $SKIP_LOG_SPLUNK  

ans_qualys:
  <<: *ansible_job_alt_proxy
  stage: ans_qualys
  script:
    - ansible-playbook -i inventory.yml --vault-id ${RUN_ENV}@ansible_vault_password play_qualys.yml
  except:
    variables:
      - $SKIP_QUALYS  

ans_promtail:
  <<: *ansible_job_alt_proxy
  stage: ans_promtail
  script:
    - ansible-playbook -i inventory.yml --vault-id ${RUN_ENV}@ansible_vault_password play_promtail.yml
  except:
    variables:
      - $SKIP_PROMTAIL  