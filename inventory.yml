all:
  vars:
    ansible_ssh_user: padmin
    ansible_user: root
    ansible_become: yes
    ansible_become_method: 'sudo'
    ansible_ssh_private_key_file: 'id_rsa'
    # proxy_host: http://cacheproxy.hcnet.vn:8080
    # proxy_host: http://proxy-server.homecredit.vn:8080
    # proxy_host: http://************:8080
    allow_world_readable_tmpfiles: true
    ansible_python_interpreter: python
    ansible_ssh_common_args: '-o StrictHostKeyChecking=no'
    
    # global_ssh_private_key_file: id_rsa_sysops
    global_ssh_private_key_file: id_rsa           
    global_update_os_in_baseline: false
    global_reboot_after_baseline: false
    global_oracle_linux_repo_env: prod
    global_oracle_linux_disable_os_repos: yes
    global_cluster_env_type: prod
    global_ic2_enabled: false
    global_ic2_team: vn_sysadmin
    ic2_teamgroup: linux
    global_dc: "vn_pdc"

    global_own_teams: 
      - vn_sysadmin
    global_dns_servers:
      vn_pdc:
        - "***********"
        - "***********"      
      
    global_register_to_ip_plan: true
    global_register_install_S1: true

    # os baseline vars
    global_env_type: prod
    global_ntp_servers:
      global_dc:
        - ntp1.vn.prod
        - ntp2.vn.prod
        - ntp3.vn.prod
    global_qradar_host: qradar.vn.prod

    dns_api_url: https://run-tools.homecredit.vn/dnsphpadmin/api.php
    dns_api_token: !vault |
              $ANSIBLE_VAULT;1.2;AES256;nonprod
              34656439633232306239613439343534336466393536363066663464303539663635666532623534
              3465653165623535333163636336653465646230353530620a333366636433656437373532353565
              37396663643935613634313834333866313866616362626133393033623539623666646565646465
              3930613336393831630a363461623636306334633966643565366138663234366164626537663765
              38356530353865343730383138616631386334333264323339616565613839303161376637623730
              3538383337313934336335386336336264366338343961383966
    dns_zone: hcnet.vn  

  children:
    allserver:
      hosts:     
        lbproxyprod01.hcnet.vn:
          ansible_host: ***********
        lbproxyprod02.hcnet.vn:
          ansible_host: ***********     
        proxy-server01.hcnet.vn:
          ansible_host: *********** 
        proxy-server02.hcnet.vn:
          ansible_host: *********** 
        proxy-server03.hcnet.vn:
          ansible_host: *********** 
        proxy-serverdrc01.hcnet.vn:
          ansible_host: ***********
        proxy-serverdrc02.hcnet.vn:
          ansible_host: ***********                          
    proxy:
      hosts:    
        proxy-server01.hcnet.vn:
          ansible_host: *********** 
        proxy-server02.hcnet.vn:
          ansible_host: *********** 
        proxy-server03.hcnet.vn:
          ansible_host: *********** 
        proxy-serverdrc01.hcnet.vn:
          ansible_host: ***********
        proxy-serverdrc02.hcnet.vn:
          ansible_host: ***********
    # lb:
    #   hosts:
    #     lbproxyprod01.hcnet.vn:
    #       ansible_host: ***********
    #     lbproxyprod02.hcnet.vn:
    #       ansible_host: ***********
    squid-pdc:
      hosts:    
        proxy-server01.hcnet.vn:
          ansible_host: *********** 
        proxy-server02.hcnet.vn:
          ansible_host: *********** 
        proxy-server03.hcnet.vn:
          ansible_host: ***********    
    squid-drc: 
      hosts:      
        proxy-serverdrc01.hcnet.vn:
          ansible_host: ***********
        proxy-serverdrc02.hcnet.vn:
          ansible_host: ***********                   