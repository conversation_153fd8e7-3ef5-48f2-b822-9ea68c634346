global
 chroot /var/lib/haproxy
 daemon
 group haproxy
 maxconn 20000
 pidfile /var/run/haproxy.pid
 user haproxy

defaults
 log global
 maxconn 20000
 option redispatch
 retries 3
 timeout http-request 15s
 timeout queue 3m
 timeout connect 5m
 timeout client 7m
 timeout server 7m

listen stats
 bind {{ ipaddress }}:8081
 mode http
 stats enable
 stats uri /stats
 stats realm HAProxy\ Statistics
 stats auth admin:P@ssword1

listen squid
 bind {{ vip }}:8080
 mode tcp
 option tcp-check
 tcp-check connect port 8080
 #balance roundrobin
 balance source
 server squid01 ***********:8080 check inter 5000 rise 2 fall 5 send-proxy
 server squid02 ***********:8080 check inter 5000 rise 2 fall 5 send-proxy
 server squid03 ***********:8080 check inter 5000 rise 2 fall 5 send-proxy
