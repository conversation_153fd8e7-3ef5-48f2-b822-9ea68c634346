- name: General baseline setup all VMs
  hosts: all
  # serial: 100%
  # become: yes
  gather_facts: true
  
  tags:
  - general_setup_baseline_all
  
  vars_files: 
    - secrets.yml
    # - ic2_url_password.yml

  vars:  
    # playbook variables mapping from global variables with default
    update_os: "{{ global_update_os_in_baseline | default(true) }}"
    reboot_after_baseline: "{{ global_reboot_after_baseline | default(true) }}"
    repo_env: "{{ global_oracle_linux_repo_env | default('prod') }}"
    cluster_env_type: "{{ global_cluster_env_type|default('prod') }}"
    ic2_teamgroup: "{{ global_ic2_team | default('linux') }}"
    own_team: "{{ global_own_teams | default(['vn_sysadmin']) }}"
    ic2_custom_vars: {}
    global_register_to_ip_plan: true
    global_register_install_splunkfwd: true
    global_register_install_S1: true
    global_register_ntp: true
    global_install_node_exporter: true

    global_admin_email: '<EMAIL>'
    global_dc: "vn_pdc"
    global_dns_servers:
      vn_pdc:
        - "***********"
        - "***********"    
      vn_drc:
        - "************"
    # additional vars file need to load for this play
    global_ntp_servers:
      vn_pdc:
        - ntp1.vn.prod
        - ntp2.vn.prod
      vn_drc:
        - ntp3.vn.prod
    global_vn_timezone: 'Asia/Ho_Chi_Minh'
    global_qradar_host: qradar.vn.prod
    sentineone_agent_package: 'SentinelAgent_linux_v23_2_2_4.rpm'


  tasks:
  - name: Configure DNS resolver /etc/resolv.conf
    include_role: 
      name: dns_config
    vars:
      dns_servers: "{{ global_dns_servers[global_dc] }}"
      dns_timeout: "{{ global_dns_timeout | default(1) }}"
    tags:
    - dns_config
    when: (global_dns_servers.vn_pdc is defined) and (global_dns_servers.vn_pdc |length>0)

  - name: Normalize yum repositories
    include_role:
      name: oracle_linux_repo
    vars:
      disable_os_repos: "{{ global_oracle_linux_disable_os_repos | default(true) }}"
    tags:
    - yum_repos

  - name: Setup general OS configuration
    include_role:
      name: linux_baseline_config
    vars:
      timezone: "{{ global_vn_timezone | default('Asia/Ho_Chi_Minh') }}"
      smtp_host: "{{ global_smtp_host | default('smtp-int.vn.prod') }}"
      admin_email: "{{ global_admin_email | default('<EMAIL>') }}"
    tags:
    - baseline_config   

  - name: Set motd facts
    set_fact:
      global_motd_file: "{{cluster_env_type| regex_replace('^prod$', 'production')}}_motd"
    when: global_motd_file is not defined and cluster_env_type is defined

  - name: "Set motd global_motd_file: {{global_motd_file}}"
    include_role:
      name: dynamic_motd
    vars:
      dynamic_motd_header_file: "{{global_motd_file}}"
    tags:
    - dynamic_motd

  - name: Set PS1 {{cluster_env_type|upper}}
    copy:
      dest: "/etc/profile.d/prod_prompt.sh"
      content: |
        env_type="{{cluster_env_type|upper}}"
        env_type_msg="${env_type}"
        red="\[\e[0;31m\]"
        cyan="\[\e[0;36m\]"
        yellow="\[\e[1;33m\]"
        green="\[\e[0;32m\]"
        reset="\[\e[0m\]"
        
        # If id command returns zero, you have root access.
        if [ $(id -u) -eq 0 ];
        then # you are root, set red colour prompt
          PS1="${env_type_msg} ${red}[\u@\H:${cyan}\w${red}] ${yellow}#${reset} "
        else # normal
          PS1="${env_type_msg} ${green}[\u@\H:${cyan}\w${green}] ${yellow}\$${reset} "
        fi

  - name: Setup logger
    include_role:
      name: logger
    tags:
    - logger

  - name: Setup NTP
    include_role:
      name: ntp_client
    vars:
      ntp_servers: "{{ global_ntp_servers[global_dc] }}"
      ntp_timezone: "{{ global_vn_timezone }}"
      ntp_package: chrony
      ntp_config_file: /etc/chrony.conf
      ntp_manage_config: true
    tags:
    - ntp_client
    # when: global_ntp_servers is defined and global_dc is defined and global_vn_timezone is defined and global_ntp_servers[global_dc] is defined
    when: global_register_ntp | bool and global_register_ntp is defined

  - name: Setup Qradar
    include_role:
      name: deploy-qradar-syslog
    # vars:
    #   qradar_ip: "{{ global_qradar_host }}"
    #   template_header: '# This file is managed by ansible, please do not modify it by hand, otherwise your changes will be lost. In case you need to change anything, please contact HCVN sysop team'
    tags:
    - qradar
    when: (global_qradar_host is defined) and (global_qradar_host|length>0)

  - name: Check security_eit
    include_role:
      name: security_eit
    tags:
    - security_eit    

  - name: Install SentinelOne
    block:
    - name: Common | System detail
      debug:
        msg: "{{ item }}"
      with_items:
        - "{{ ansible_distribution }}"
        - "{{ ansible_distribution_version }}"
        - "{{ ansible_distribution_major_version }}"
        - "{{ ansible_os_family }}"

    - name: Download the SentinelOne agent package (rpm)
      get_url:
        url: "http://repo-download.homecredit.vn/repos/{{ sentineone_agent_package }}"
        dest: "/tmp/{{ sentineone_agent_package }}"
      delegate_to: 127.0.0.1
      
    
    - name: Install SentinelOne agent
      include_role:
        name: install_sentinelone_agent
      vars:
        run_env: prod
        registration_token:
          nonprod: "eyJ1cmwiOiAiaHR0cHM6Ly9ldWNlMS0xMDQuc2VudGluZWxvbmUubmV0IiwgInNpdGVfa2V5IjogImdfOTkxOWYzNTZiMzI0NzMxNSJ9"
          prod: "eyJ1cmwiOiAiaHR0cHM6Ly9ldWNlMS0xMDQuc2VudGluZWxvbmUubmV0IiwgInNpdGVfa2V5IjogImdfZTgyOWVjZmY5OGFlZTMyMyJ9"
        management_proxy:
          # nonprod: http://proxy4sever-nonprod.homecredit.vn:8080
          # prod: http://proxy4sever-prod.homecredit.vn:8080
          nonprod: http://cacheproxy.hcnet.vn:8080
          prod: http://cacheproxy.hcnet.vn:8080
        tags:
        - SentinelOne
    when: global_register_install_S1 is defined and global_register_install_S1 | bool


  - name: Install Splunk Forwarder
    include_role:
      name: install-splunk-forwarder
    vars: 
      COUNTRY_CODE: "VN"
      SPLUNK_DEPLOYMENT_SERVER: "{{ global_splunk_depser|default('dep01-pdcvn1spl.vn.prod:8089') }}"
      INDEX_NAME: "hcg_vn_sysops_prod"
      SOURCE_TYPE: "system:log"
      PATH_MONITOR: "/var/log/messages" # https://phoenixnap.com/kb/how-to-view-read-linux-log-files
    tags:
    - InstallSplunkFwd
    # when: global_splunkfwd_enabled is defined and global_splunkfwd_enabled | bool
    when: global_register_install_splunkfwd | bool and global_register_install_splunkfwd is defined

  - name: ipplan_pwd
    debug:
      msg: "{{ ipplan_pwd  }}"
      
  - name: Register IP Address to ipplan tool
    include_role:
      name: api-register-ipaddr
    vars: 
      # COUNTRY_CODE: "VN"
      # LocationDc: "VN-01-PDC"      
      v_user_name: "vnnetautom"
      v_user_pwd: "{{ ipplan_pwd }}"
      ip_state: 'present'
      server_name: "{{ dns_name }}"
      IP_Addr: "{{ ansible_host }}"      
      v_netmask: "{{ netmask | default('*************') }}"
      v_owner: "{{ owner | default('VN.JIRASD.TEAM.Managed_Services') }}"
      description: "dhcp server"
    tags:
    - registerIPaddress
    when: (global_register_to_ip_plan is defined) and (global_register_to_ip_plan | bool) 

  # - name: Install Node-Exporter
  #   include_role:
  #     name: node_exporter
  #   vars:
  #     environment:
  #     - http_proxy: "{{ proxy_host }}"
  #     - https_proxy: "{{ proxy_host }}" 
  #   when: (global_install_node_exporter is defined) and (global_install_node_exporter | bool) 
   
