# Run play_linux_joindomain.yml -> play_squid.yml (include squid + squidGuard)
---
- name: SQUID setup
  hosts: "{{ target_hosts | default('squid-drc') }}"
  vars:
    package:
      - squid
    sysctl:
      - name: net.ipv4.ip_nonlocal_bind
        value: 0
      - name: net.ipv4.ip_forward
        value: 0
      - name: net.ipv4.conf.default.rp_filter
        value: 1
      - name: net.ipv4.conf.default.accept_source_route
        value: 0
      - name: kernel.sysrq
        value: 0
      - name: kernel.core_uses_pid
        value: 1
      - name: net.ipv4.tcp_syncookies
        value: 1
      - name: net.core.rmem_max
        value: 262144
      - name: net.core.wmem_max
        value: 262144
      - name: net.ipv4.tcp_low_latency
        value: 1
      - name: net.ipv4.ip_local_port_range
        value: 1024 65000
      - name: net.ipv4.tcp_fin_timeout
        value: 30
      - name: net.ipv4.tcp_synack_retries
        value: 2
      - name: net.ipv4.tcp_syn_retries
        value: 2
      - name: fs.file-max
        value: 372925
      - name: net.ipv4.tcp_max_syn_backlog
        value: 2048
      - name: net.ipv4.tcp_syncookies
        value: 1
   
  tasks:
    - name: reset sysctl.conf
      shell: echo -n "" > /etc/sysctl.conf  
  # edit sysctl.conf
    - name: Config sysctl.conf
      sysctl:
        name: "{{ item.name }}"
        value: "{{ item.value }}"
        state: present
        sysctl_set: true
        reload: yes
      with_items: 
        - "{{ sysctl }}"     
    # Install squid
    - name: Install squid
      dnf:
        name: "{{ package }}"
        state: present
    # Enable squid
    - name: Enable service squid, and not touch the running state and start service
      service:
        name: squid
        state: started
        enabled: yes

    # Create /DATA
    - name: Create Folder Logs
      file:
        path: /DATA/squid-logs/
        state: directory
        owner: squid
        group: squid
        mode: '0755'
    # Create folder cache
    - name: Create Folder cache
      file:
        path: "{{ item }}"
        state: directory
        owner: squid
        group: squid
        mode: '0755'
      loop:
        - /DATA/cache
        - /DATA/cache1
        - /DATA/cache2
        - /DATA/cache3 
    # Copy file acl config
    - name: Upload domain list in squid
      copy:
        dest: "/etc/squid/"
        src: "{{ item }}"
        mode: 0644
        owner: root
        group: root
      with_items:
        - ads_blocked_urls
        - atp_block_domains
        - atp_block_ip
        - blacklist_urls
        - block_src
        - whitelist_urls
        - allow_src
    - name: copy squid to pdc server
      copy:
        src: "squid-pdc.conf"
        dest: "/etc/squid/squid.conf"
        mode: 0644
        owner: root
        group: root 
      when: (inventory_hostname in groups['squid-pdc'])

    - name: copy squid to drc server
      copy:
        src: "squid-drc.conf"
        dest: "/etc/squid/squid.conf"
        mode: 0644
        owner: root
        group: root
      when: (inventory_hostname in groups['squid-drc'])

    - name: check config Squid
      shell: squid -k check
      register: check_config_Squid
      changed_when: False
      
    - name: print
      debug: 
        msg: "{{ check_config_Squid}}"
      when: check_config_Squid.stderr != ""
      changed_when: False

    - name: check config Squid
      shell: squid -k reconfigure
      register: check_config_Squid
      when: check_config_Squid.stderr == ""
      
    # - name: restart squid
    #   service: name=squid state=restarted
    #   when: check_config_Squid.stderr == ""