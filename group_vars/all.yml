# os baseline vars
ipaddress: "{{ ansible_default_ipv4['address'] }}"
#ipaddress: "{{ ansible_eth0.ipv4.address }}"
squidhostname: "{{ inventory_hostname }}"
global_env_type: prod
global_register_to_ip_plan: true
global_update_os_in_baseline: true
global_reboot_after_baseline: true
global_oracle_linux_repo_env: prod
global_oracle_linux_disable_os_repos: yes
global_ic2_enabled: false
global_ic2_team: linux
global_own_teams: 
  - vn_sysadmin
global_ic2_custom_vars: {}
global_ntp_servers:
  global_dc:
    - ntp1.vn.prod
    - ntp2.vn.prod
    - ntp3.vn.prod

dns_api_url: https://run-tools.homecredit.vn/dnsphpadmin/api.php
dns_api_token: !vault |
          $ANSIBLE_VAULT;1.2;AES256;nonprod
          34656439633232306239613439343534336466393536363066663464303539663635666532623534
          3465653165623535333163636336653465646230353530620a333366636433656437373532353565
          37396663643935613634313834333866313866616362626133393033623539623666646565646465
          3930613336393831630a363461623636306334633966643565366138663234366164626537663765
          38356530353865343730383138616631386334333264323339616565613839303161376637623730
          3538383337313934336335386336336264366338343961383966
dns_zone: hcnet.vn  