variable "vsphere_user_pdc" {
  description = "vsphere server for the pdc environment - EXAMPLE: vsphereuser"
  
}

variable "vsphere_password_pdc" {
  description = "vsphere server password for the pdc environment"
}

# PDC
variable "vsphere_server_pdc" {
  description = "vsphere server for the environment - EXAMPLE: vcenter01.hosted.local"
  default     = "vcenter-hcm.vn.prod"
}

variable "vsphere_datacenter_name_pdc" {
  description = "Vsphere DC name"
  default     = "VN-HCM"
}

variable "vsphere_datastore_name_pdc" {
  description = "Vsphere Data Storage name PDC"
  default     = "VN-HCM-DEV-LAN"
}

variable "vsphere_cluster_name_pdc" {
  description = "Vsphere Cluster name PDC"
  default     = "DEV-LAN"
}

variable "vsphere_network_pdc" {
  type    = map
  description = "Vsphere network in PDC"
  default     = {
    name = "VN-HCM-PR-VLAN40" 
    default_gw = "************"
  }
}

# DRC
variable "vsphere_password_drc"{}

variable "vsphere_server_drc" {
  default     = "vcenter-bnd.vn.prod"
}

variable "vsphere_network_drc" {
  type    = map
  description = "Vsphere network in DRC"
  default     = {
    name = "VN-BND-PR-VLAN40" 
    default_gw = "************"
}
}

variable "vsphere_datacenter_name_drc" {    
}

variable "vsphere_cluster_name_drc" {
  
}

variable "vsphere_datastore_name_drc" {
  
}

variable "vsphere_user_drc" {
  
}


# VM template
variable "vsphere_vm_template_name" {
  description = "Vsphere VM template name"
  default     = "ol7_dynamic_disk"
}

variable "vsphere_vm_template_name_ol8" {}

variable "vsphere_vm_template_name_ol9" {
  
}

# vm1
variable "vm1_ip" {
}
variable "vm1_name" {
}
# vm2
variable "vm2_ip" {
}
variable "vm2_name" {
}
# vm3
variable "vm3_ip" {
}
variable "vm3_name" {
}

# vm4
variable "vm4_ip" {
}
variable "vm4_name" {
}

variable "vm5_ip" {
}
variable "vm5_name" {
}

variable "vm6_ip" {
}
variable "vm6_name" {
}
variable "vm7_ip" {
}
variable "vm7_name" {
}

# variable "vm_cpu_count" {
# }

# variable "vm_memory_size" {
# }

# variable "vm_cpu_count_1" {
# }

# variable "vm_memory_size_1" {
# }

# variable "vm_cpu_count_2" {
# }

# variable "vm_memory_size_2" {
# }

# variable "vm_cpu_count_3" {
# }

# variable "vm_memory_size_3" {
# }

# variable "vm_cpu_count_4" {
# }

# variable "vm_memory_size_4" {
# }
# variable "vm_cpu_count_5" {
# }

# variable "vm_memory_size_5" {
# }
# variable "vm_cpu_count_6" {
# }

# variable "vm_memory_size_6" {
# }

variable "vm_cpu_count_7" {
}
variable "vm_memory_size_7" {
}

variable "vm_cores_per_socket" {
  default = "2"  
}
