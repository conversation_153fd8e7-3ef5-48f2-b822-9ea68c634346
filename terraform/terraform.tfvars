#vsphere_server user
vsphere_user_pdc = "<EMAIL>"
vsphere_user_drc = "<EMAIL>"
# vsphere_user_gen = "<EMAIL>"

#vsphere pdc
vsphere_datacenter_name_pdc = "VN-HCM"

# vsphere PDC
vsphere_cluster_name_pdc = "VN-HCM-PR" 
vsphere_network_pdc = {
        name = "VN-HCM-PR-VLAN1217"
        default_gw = "***********4"
    }


vsphere_vm_template_name = "ol7_dynamic_disk"
vsphere_vm_template_name_ol8 = "ol8_dynamic_disk_20230216"
vsphere_vm_template_name_ol9 = "template_oraclelinux9_20230725"
vsphere_datastore_name_pdc = "VN-HCM-PR"

# vsphere DRC
vsphere_datacenter_name_drc = "VN-BND"
vsphere_cluster_name_drc = "VN-BND-PR"
vsphere_datastore_name_drc = "VN-BND-PR"

# vsphere_network_drc = {
#     name = "VN-BND-PR-VLAN1217" 
#     default_gw = "10.17.92.254"
# }    

vsphere_network_drc = {
    name = "VN-BND-PR-DMZ-VLAN270" 
    default_gw = "10.17.170.254"
}   

# vsphere_vm_template_name = "oracle-linux-7-sysops-05012022"

# VM sizing
vm_cpu_count_master = "4"
vm_memory_size_master = "8192"
vm_cores_per_socket= "2"

# sizing
# VM sizing
# vm_cpu_count_1 = "8"
# vm_memory_size_1 = "16384"

# vm_cpu_count_2 = "8"
# vm_memory_size_2 = "16384"

# vm_cpu_count_3 = "8"
# vm_memory_size_3 = "16384"

# vm_cpu_count_4 = "4"
# vm_memory_size_4 = "8192"
# vm_cpu_count_5 = "4"
# vm_memory_size_5 = "8192"

# vm_cpu_count_6 = "8"
# vm_memory_size_6 = "16384"

vm_cpu_count_7 = "4"
vm_memory_size_7 = "8196"

##### VM definitions
# vm1
# vm1_ip = "***********"
# vm1_name = "proxy-server01"

# # vm2
# vm2_ip = "***********"
# vm2_name = "proxy-server02"

# vm3_ip = "***********"
# vm3_name = "proxy-server03"
# # vm3
# vm4_ip = "***********"
# vm4_name = "lbproxyprod01"
# # vm4
# vm5_ip = "***********"
# vm5_name = "lbproxyprod02"

# #vm5
# vm6_ip = "***********"
# vm6_name = "proxy-serverdrc01"

vm7_ip = "************"
vm7_name = "api-internal-drc"

# dns settings
dns_domain = "hcnet.vn"
resolvers_dns_domain = ["hcnet.vn"]
dns_servers_list = ["***********", "***********","************","************"]
