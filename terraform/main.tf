# The Provider block sets up the vSphere provider - How to connect to vCenter. Note the use of
# variables to avoid hardcoding credentials here

provider "vsphere" {
  user                 = var.vsphere_user_pdc
  password             = var.vsphere_password_pdc
  vsphere_server       = var.vsphere_server_pdc
  allow_unverified_ssl = true
  alias = "pdc"
}

#B.O DRC
provider "vsphere" {
  user                 = var.vsphere_user_drc
  password             = var.vsphere_password_drc
  vsphere_server       = var.vsphere_server_drc
  allow_unverified_ssl = true
  alias = "drc"
}


# The Data sections are about determining where the virtual machine will be placed.
# Here we are naming the vSphere DC, the cluster, datastore, virtual network and the template
# name. These are called upon later when provisioning the VM resource

# PDC

# module vm01 {
#   providers = {
#      vsphere = vsphere.pdc
#   }
#   source = "**********************:country/vn/devops/terraform/vsphere_vm.git?ref=v1.0"
#   vsphere_datacenter_name = var.vsphere_datacenter_name_pdc
#   vsphere_datastore_name = var.vsphere_datastore_name_pdc
#   vsphere_cluster_name = var.vsphere_cluster_name_pdc
#   # vsphere_vm_template_name = var.vsphere_vm_template_name_ol8
#   vsphere_vm_template_name = var.vsphere_vm_template_name_ol9
#   use_datastore_cluster = true
#   vm_cpu_count = var.vm_cpu_count_1
#   vm_cores_per_socket = var.vm_cores_per_socket
#   vm_memory_size = var.vm_memory_size_1
#   vsphere_network_name = var.vsphere_network_pdc["name"]
#   vm_ip = var.vm1_ip
#   vm_name = var.vm1_name
#   default_gw = var.vsphere_network_pdc["default_gw"]
#   additional_disks = [ 
#     { #/dev/sdd => /DATA1: external
#       size   = 60, 
#       number = 3     
#     }
#   ]  
#   vm_tags = [ 
#     # "urn:vmomi:InventoryServiceTag:336c5f7c-80bb-4782-9444-ee1e6dbb4ef4:GLOBAL", // owner: VN.JIRASD.TEAM.Contact_Center
#     "urn:vmomi:InventoryServiceTag:39456dd9-c32f-4b67-bf9c-9f447119b132:GLOBAL", // owner: VN.JIRASD.TEAM.Managed_Services
#     "urn:vmomi:InventoryServiceTag:7a586268-57d0-4a0e-b79e-a68ae44dfe2d:GLOBAL", // enviroment: Production
#     "urn:vmomi:InventoryServiceTag:560750cd-a486-4aa5-a1d2-1940ebc3358f:GLOBAL", // country: VN
#     # "urn:vmomi:InventoryServiceTag:0d1040ed-7353-4c94-993f-417b12c93972:GLOBAL", // Responsible Team: VN.JIRASD.TEAM.Contact_Center 
#     "urn:vmomi:InventoryServiceTag:7c0729ce-03af-4857-8de7-d8a00ae95257:GLOBAL", // Responsible Team: VN.JIRASD.TEAM.Managed_Services 
#     "urn:vmomi:InventoryServiceTag:d30eb765-e620-4571-b1fc-a7ca70cab714:GLOBAL",  // Provisioning: terraform_vn
#     "urn:vmomi:InventoryServiceTag:537ac466-a12d-4192-88ac-e8aa1152f0e8:GLOBAL"  // storage: skip_backup  
#   ]
# }

# module vm02 {
#   providers = {
#      vsphere = vsphere.pdc
#   }
#   source = "**********************:country/vn/devops/terraform/vsphere_vm.git?ref=v1.0"
#   vsphere_datacenter_name = var.vsphere_datacenter_name_pdc
#   vsphere_datastore_name = var.vsphere_datastore_name_pdc
#   vsphere_cluster_name = var.vsphere_cluster_name_pdc
#   # vsphere_vm_template_name = var.vsphere_vm_template_name_ol8
#   vsphere_vm_template_name = var.vsphere_vm_template_name_ol9
#   use_datastore_cluster = true
#   vm_cpu_count = var.vm_cpu_count_2
#   vm_cores_per_socket = var.vm_cores_per_socket
#   vm_memory_size = var.vm_memory_size_2
#   vsphere_network_name = var.vsphere_network_pdc["name"]
#   vm_ip = var.vm2_ip
#   vm_name = var.vm2_name
#   default_gw = var.vsphere_network_pdc["default_gw"]
#   additional_disks = [ 
#     { #/dev/sdd => /DATA1: external
#       size   = 60, 
#       number = 3     
#     }
#   ]  
#   vm_tags = [ 
#     # "urn:vmomi:InventoryServiceTag:336c5f7c-80bb-4782-9444-ee1e6dbb4ef4:GLOBAL", // owner: VN.JIRASD.TEAM.Contact_Center
#     "urn:vmomi:InventoryServiceTag:39456dd9-c32f-4b67-bf9c-9f447119b132:GLOBAL", // owner: VN.JIRASD.TEAM.Managed_Services
#     "urn:vmomi:InventoryServiceTag:7a586268-57d0-4a0e-b79e-a68ae44dfe2d:GLOBAL", // enviroment: Production
#     "urn:vmomi:InventoryServiceTag:560750cd-a486-4aa5-a1d2-1940ebc3358f:GLOBAL", // country: VN
#     # "urn:vmomi:InventoryServiceTag:0d1040ed-7353-4c94-993f-417b12c93972:GLOBAL", // Responsible Team: VN.JIRASD.TEAM.Contact_Center 
#     "urn:vmomi:InventoryServiceTag:7c0729ce-03af-4857-8de7-d8a00ae95257:GLOBAL", // Responsible Team: VN.JIRASD.TEAM.Managed_Services 
#     "urn:vmomi:InventoryServiceTag:d30eb765-e620-4571-b1fc-a7ca70cab714:GLOBAL",  // Provisioning: terraform_vn
#     "urn:vmomi:InventoryServiceTag:537ac466-a12d-4192-88ac-e8aa1152f0e8:GLOBAL"  // storage: skip_backup  
#   ]
# }

# module vm03 {
#   providers = {
#      vsphere = vsphere.pdc
#   }
#   source = "**********************:country/vn/devops/terraform/vsphere_vm.git?ref=v1.0"
#   vsphere_datacenter_name = var.vsphere_datacenter_name_pdc
#   vsphere_datastore_name = var.vsphere_datastore_name_pdc
#   vsphere_cluster_name = var.vsphere_cluster_name_pdc
#   # vsphere_vm_template_name = var.vsphere_vm_template_name_ol8
#   vsphere_vm_template_name = var.vsphere_vm_template_name_ol9
#   use_datastore_cluster = true
#   vm_cpu_count = var.vm_cpu_count_3
#   vm_cores_per_socket = var.vm_cores_per_socket
#   vm_memory_size = var.vm_memory_size_3
#   vsphere_network_name = var.vsphere_network_pdc["name"]
#   vm_ip = var.vm3_ip
#   vm_name = var.vm3_name
#   default_gw = var.vsphere_network_pdc["default_gw"]
#   additional_disks = [ 
#     { #/dev/sdd => /DATA1: external
#       size   = 60, 
#       number = 3     
#     }
#   ]  
#   vm_tags = [ 
#     # "urn:vmomi:InventoryServiceTag:336c5f7c-80bb-4782-9444-ee1e6dbb4ef4:GLOBAL", // owner: VN.JIRASD.TEAM.Contact_Center
#     "urn:vmomi:InventoryServiceTag:39456dd9-c32f-4b67-bf9c-9f447119b132:GLOBAL", // owner: VN.JIRASD.TEAM.Managed_Services
#     "urn:vmomi:InventoryServiceTag:7a586268-57d0-4a0e-b79e-a68ae44dfe2d:GLOBAL", // enviroment: Production
#     "urn:vmomi:InventoryServiceTag:560750cd-a486-4aa5-a1d2-1940ebc3358f:GLOBAL", // country: VN
#     # "urn:vmomi:InventoryServiceTag:0d1040ed-7353-4c94-993f-417b12c93972:GLOBAL", // Responsible Team: VN.JIRASD.TEAM.Contact_Center 
#     "urn:vmomi:InventoryServiceTag:7c0729ce-03af-4857-8de7-d8a00ae95257:GLOBAL", // Responsible Team: VN.JIRASD.TEAM.Managed_Services 
#     "urn:vmomi:InventoryServiceTag:d30eb765-e620-4571-b1fc-a7ca70cab714:GLOBAL",  // Provisioning: terraform_vn
#     "urn:vmomi:InventoryServiceTag:537ac466-a12d-4192-88ac-e8aa1152f0e8:GLOBAL"  // storage: skip_backup  
#   ]
# }
# module vm04 {
#   providers = {
#      vsphere = vsphere.pdc
#   }
#   source = "**********************:country/vn/devops/terraform/vsphere_vm.git?ref=v1.0"
#   vsphere_datacenter_name = var.vsphere_datacenter_name_pdc
#   vsphere_datastore_name = var.vsphere_datastore_name_pdc
#   vsphere_cluster_name = var.vsphere_cluster_name_pdc
#   # vsphere_vm_template_name = var.vsphere_vm_template_name_ol8
#   vsphere_vm_template_name = var.vsphere_vm_template_name_ol9
#   use_datastore_cluster = true
#   vm_cpu_count = var.vm_cpu_count_4
#   vm_cores_per_socket = var.vm_cores_per_socket
#   vm_memory_size = var.vm_memory_size_4
#   vsphere_network_name = var.vsphere_network_pdc["name"]
#   vm_ip = var.vm4_ip
#   vm_name = var.vm4_name
#   default_gw = var.vsphere_network_pdc["default_gw"]
#   additional_disks = [ 
#     { #/dev/sdd => /DATA1: external
#       size   = 60, 
#       number = 3     
#     }
#   ]  
#   vm_tags = [ 
#     # "urn:vmomi:InventoryServiceTag:336c5f7c-80bb-4782-9444-ee1e6dbb4ef4:GLOBAL", // owner: VN.JIRASD.TEAM.Contact_Center
#     "urn:vmomi:InventoryServiceTag:39456dd9-c32f-4b67-bf9c-9f447119b132:GLOBAL", // owner: VN.JIRASD.TEAM.Managed_Services
#     "urn:vmomi:InventoryServiceTag:7a586268-57d0-4a0e-b79e-a68ae44dfe2d:GLOBAL", // enviroment: Production
#     "urn:vmomi:InventoryServiceTag:560750cd-a486-4aa5-a1d2-1940ebc3358f:GLOBAL", // country: VN
#     # "urn:vmomi:InventoryServiceTag:0d1040ed-7353-4c94-993f-417b12c93972:GLOBAL", // Responsible Team: VN.JIRASD.TEAM.Contact_Center 
#     "urn:vmomi:InventoryServiceTag:7c0729ce-03af-4857-8de7-d8a00ae95257:GLOBAL", // Responsible Team: VN.JIRASD.TEAM.Managed_Services 
#     "urn:vmomi:InventoryServiceTag:d30eb765-e620-4571-b1fc-a7ca70cab714:GLOBAL",  // Provisioning: terraform_vn
#     "urn:vmomi:InventoryServiceTag:537ac466-a12d-4192-88ac-e8aa1152f0e8:GLOBAL"  // storage: skip_backup  
#   ]
# }
# module vm05 {
#   providers = {
#      vsphere = vsphere.pdc
#   }
#   source = "**********************:country/vn/devops/terraform/vsphere_vm.git?ref=v1.0"
#   vsphere_datacenter_name = var.vsphere_datacenter_name_pdc
#   vsphere_datastore_name = var.vsphere_datastore_name_pdc
#   vsphere_cluster_name = var.vsphere_cluster_name_pdc
#   # vsphere_vm_template_name = var.vsphere_vm_template_name_ol8
#   vsphere_vm_template_name = var.vsphere_vm_template_name_ol9
#   use_datastore_cluster = true
#   vm_cpu_count = var.vm_cpu_count_5
#   vm_cores_per_socket = var.vm_cores_per_socket
#   vm_memory_size = var.vm_memory_size_5
#   vsphere_network_name = var.vsphere_network_pdc["name"]
#   vm_ip = var.vm5_ip
#   vm_name = var.vm5_name
#   default_gw = var.vsphere_network_pdc["default_gw"]
#   additional_disks = [ 
#     { #/dev/sdd => /DATA1: external
#       size   = 60, 
#       number = 3     
#     }
#   ]  
#   vm_tags = [ 
#     # "urn:vmomi:InventoryServiceTag:336c5f7c-80bb-4782-9444-ee1e6dbb4ef4:GLOBAL", // owner: VN.JIRASD.TEAM.Contact_Center
#     "urn:vmomi:InventoryServiceTag:39456dd9-c32f-4b67-bf9c-9f447119b132:GLOBAL", // owner: VN.JIRASD.TEAM.Managed_Services
#     "urn:vmomi:InventoryServiceTag:7a586268-57d0-4a0e-b79e-a68ae44dfe2d:GLOBAL", // enviroment: Production
#     "urn:vmomi:InventoryServiceTag:560750cd-a486-4aa5-a1d2-1940ebc3358f:GLOBAL", // country: VN
#     # "urn:vmomi:InventoryServiceTag:0d1040ed-7353-4c94-993f-417b12c93972:GLOBAL", // Responsible Team: VN.JIRASD.TEAM.Contact_Center 
#     "urn:vmomi:InventoryServiceTag:7c0729ce-03af-4857-8de7-d8a00ae95257:GLOBAL", // Responsible Team: VN.JIRASD.TEAM.Managed_Services 
#     "urn:vmomi:InventoryServiceTag:d30eb765-e620-4571-b1fc-a7ca70cab714:GLOBAL",  // Provisioning: terraform_vn
#     "urn:vmomi:InventoryServiceTag:537ac466-a12d-4192-88ac-e8aa1152f0e8:GLOBAL"  // storage: skip_backup  
#   ]
# }
# # # DRC
# module vm06 {
#   providers = {
#      vsphere = vsphere.drc
#   }
#   source = "**********************:country/vn/devops/terraform/vsphere_vm.git?ref=v1.0"
#   vsphere_datacenter_name = var.vsphere_datacenter_name_drc
#   vsphere_datastore_name = var.vsphere_datastore_name_drc
#   vsphere_cluster_name = var.vsphere_cluster_name_drc
#   vsphere_vm_template_name = var.vsphere_vm_template_name_ol9
#   use_datastore_cluster = true
#   vm_cpu_count = var.vm_cpu_count_6
#   vm_cores_per_socket = var.vm_cores_per_socket
#   vm_memory_size = var.vm_memory_size_6
#   vsphere_network_name = var.vsphere_network_drc["name"]
#   vm_ip = var.vm6_ip
#   vm_name = var.vm6_name
#   default_gw = var.vsphere_network_drc["default_gw"]
#   additional_disks = [ 
#     { #/dev/sdd => /DATA1: external
#       size   = 60, 
#       number = 3     
#     }
#   ]  
#   vm_tags = [ 
#     # "urn:vmomi:InventoryServiceTag:336c5f7c-80bb-4782-9444-ee1e6dbb4ef4:GLOBAL", // owner: VN.JIRASD.TEAM.Contact_Center
#     "urn:vmomi:InventoryServiceTag:feec7b55-b8de-46ba-a14c-8d893cf9dbed:GLOBAL", // owner: VN.JIRASD.TEAM.Managed_Services 
#     "urn:vmomi:InventoryServiceTag:cb659321-88a4-48f1-b180-be1cb9477d58:GLOBAL", // enviroment: Production
#     "urn:vmomi:InventoryServiceTag:2a217a89-9177-4cc7-803f-b3c8f460ebaf:GLOBAL", // country: VN
#     # "urn:vmomi:InventoryServiceTag:0d1040ed-7353-4c94-993f-417b12c93972:GLOBAL", // Responsible Team: VN.JIRASD.TEAM.Contact_Center 
#     "urn:vmomi:InventoryServiceTag:8788aab3-8c28-482e-b81b-d4554d9e4723:GLOBAL", // Responsible Team: VN.JIRASD.TEAM.Managed_Services  
#     "urn:vmomi:InventoryServiceTag:01ec9cc2-5a2c-4fb9-b651-a3af53be53af:GLOBAL",  // Provisioning: terraform_vn
#     "urn:vmomi:InventoryServiceTag:703fc57b-2164-4848-be41-495cb7f771be:GLOBAL" // storage: skip_backup
#   ]
# }

module vm07 {
  providers = {
     vsphere = vsphere.drc
  }
  source = "**********************:country/vn/devops/terraform/vsphere_vm.git?ref=v1.0"
  vsphere_datacenter_name = var.vsphere_datacenter_name_drc
  vsphere_datastore_name = var.vsphere_datastore_name_drc
  vsphere_cluster_name = var.vsphere_cluster_name_drc
  vsphere_vm_template_name = var.vsphere_vm_template_name_ol8
  use_datastore_cluster = true
  vm_cpu_count = var.vm_cpu_count_7
  vm_cores_per_socket = var.vm_cores_per_socket
  vm_memory_size = var.vm_memory_size_7
  vsphere_network_name = var.vsphere_network_drc["name"]
  vm_ip = var.vm7_ip
  vm_name = var.vm7_name
  default_gw = var.vsphere_network_drc["default_gw"]
  additional_disks = [ 
    { #/dev/sdd => /DATA1: external
      size   = 60, 
      number = 3     
    }
  ]  
  vm_tags = [ 
    # "urn:vmomi:InventoryServiceTag:336c5f7c-80bb-4782-9444-ee1e6dbb4ef4:GLOBAL", // owner: VN.JIRASD.TEAM.Contact_Center
    "urn:vmomi:InventoryServiceTag:feec7b55-b8de-46ba-a14c-8d893cf9dbed:GLOBAL", // owner: VN.JIRASD.TEAM.Managed_Services 
    "urn:vmomi:InventoryServiceTag:cb659321-88a4-48f1-b180-be1cb9477d58:GLOBAL", // enviroment: Production
    "urn:vmomi:InventoryServiceTag:2a217a89-9177-4cc7-803f-b3c8f460ebaf:GLOBAL", // country: VN
    # "urn:vmomi:InventoryServiceTag:0d1040ed-7353-4c94-993f-417b12c93972:GLOBAL", // Responsible Team: VN.JIRASD.TEAM.Contact_Center 
    "urn:vmomi:InventoryServiceTag:8788aab3-8c28-482e-b81b-d4554d9e4723:GLOBAL", // Responsible Team: VN.JIRASD.TEAM.Managed_Services  
    "urn:vmomi:InventoryServiceTag:01ec9cc2-5a2c-4fb9-b651-a3af53be53af:GLOBAL",  // Provisioning: terraform_vn
    "urn:vmomi:InventoryServiceTag:703fc57b-2164-4848-be41-495cb7f771be:GLOBAL" // storage: skip_backup
  ]
}
