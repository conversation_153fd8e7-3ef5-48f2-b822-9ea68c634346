---
- name: Keepalived DRC
  hosts: squid-drc
  vars:
    package:
      - keepalived
  tasks:     
    # Install keepalived
    - name: Install keepalived
      dnf:
        name: "{{ package }}"
        state: present

    #copy check_script.sh
    - name: check_script.sh
      copy:
        src: "drc/check_script.sh"
        dest: "/etc/keepalived/check_script.sh"
        mode: 0755
        owner: root
        group: root 

    - name: copy to master keepalived.conf
      copy:
        src: "drc/keepalived-drc-master.conf"
        dest: "/etc/keepalived/keepalived.conf"
        mode: 0644
        owner: root
        group: root 

    - name: copy to backup keepalived.conf
      copy:
        src: "drc/keepalived-drc-slave.conf"
        dest: "/etc/keepalived/keepalived.conf"
        mode: 0644
        owner: root
        group: root

    # Enable keepalived and haproxy
    - name: Enable service keepalived, and not touch the running state
      service:
        name: keepalived
        enabled: yes
    - name: restart keepalived
      service: name=keepalived state=restarted