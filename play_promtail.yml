- name:           Install Promtail
  hosts:          squid-drc,squid-pdc
  gather_facts:   true
  vars:
    promtail_version: "2.6.1"
    promtail_download_url: "https://github.com/grafana/loki/releases/download/v{{ promtail_version }}/promtail-linux-amd64.zip"
    install_dir: "/usr/local/bin"
    config_dir: "/etc/promtail"
    log_dir: "/var/lib/promtail"
    loki_url: "https://loki.apps-country-k8s-pdc-prod-green.hcnet.vn/loki/api/v1/push"
    tenant_id: "platform"

    # log_sources: // defined in group_vars
    #   - job_name: "squid"
    #     job_label: "squidlogs"
    #     log_path: "/DATA/squid-logs/access.log"
      # - job_name: "dnslogs"
      #   job_label: "dnslogs"
      #   log_path: "/var/log/messages"
  tasks:   
    - name: install promtail
      include_role:
        name: ansible-promtail